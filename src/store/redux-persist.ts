import { PersistConfig } from "redux-persist";
import { AppState } from "./store";
import storageSession from "redux-persist/lib/storage/session";
import { MetaWithPersist } from "./apps/meta";

const persistList = [MetaWithPersist.reducerPath];

// const PERSIST_CACHE_KEY = "reduxPersistedAt";

// export const cacheExpirationMiddleware: Middleware = store => (next: Dispatch<AnyAction>) => (action: AnyAction) => {
//   const result = next(action);

//   // Ensure we're in a browser environment
//   if (typeof window !== "undefined") {
//     // After the state is updated, check if the data should be cached
//     const persistedAt = window.localStorage.getItem(PERSIST_CACHE_KEY);

//     if (!persistedAt) {
//       // If there's no timestamp, set it now
//       window.localStorage.setItem(PERSIST_CACHE_KEY, Date.now().toString());
//     } else {
//       const now = Date.now();

//       if (now - Number(persistedAt) > Number(process.env.NEXT_PUBLIC_REDUX_PERSIST_TTL_IN_MS || 3600000)) {
//         // Cache is expired, clear persisted state and timestamp
//         window.localStorage.removeItem(PERSIST_CACHE_KEY);

//         const reduxStore = store as Store<any, AnyAction>;
//         const persistor = persistStore(reduxStore);
//         persistor.purge();
//       }
//     }
//   }

//   return result;
// };

export const persistConfig: PersistConfig<AppState> = {
  key: "root",
  storage: storageSession,
  whitelist: persistList,
  blacklist: []
};
