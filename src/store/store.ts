import { Supplier } from "./apps/supplier/query";
import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { AnyAction, combineReducers } from "redux";
import ConfigReducer from "./apps/config/ConfigSlice";
import ChatsReducer from "./apps/chat/ChatSlice";
import { Auth } from "./apps/auth";
import { Retailer } from "./apps/retailer";
import { Meta, MetaWithPersist } from "./apps/meta";
import { RetailerProduct } from "./apps/retailerProduct";
import { Product } from "./apps/product";
import { Order } from "./apps/order";
import { SupplierDashboard } from "./apps/supplierDashboard";
import RetailerStoreReducer from "./apps/retailerStore/retailerStoreSlice";
import { Conversation } from "./apps/conversation";
import { persistReducer, persistStore } from "redux-persist";
import { persistConfig } from "./redux-persist";

// Combine all reducers into the rootReducer
const appReducer = combineReducers({
  chatReducer: ChatsReducer,
  config: ConfigReducer,
  retailerStore: RetailerStoreReducer,
  [Auth.reducerPath]: Auth.reducer,
  [Supplier.reducerPath]: Supplier.reducer,
  [Retailer.reducerPath]: Retailer.reducer,
  [Meta.reducerPath]: Meta.reducer,
  [MetaWithPersist.reducerPath]: MetaWithPersist.reducer,
  [RetailerProduct.reducerPath]: RetailerProduct.reducer,
  [Product.reducerPath]: Product.reducer,
  [Order.reducerPath]: Order.reducer,
  [SupplierDashboard.reducerPath]: SupplierDashboard.reducer,
  [Conversation.reducerPath]: Conversation.reducer
});

// Root reducer that handles resetting the state on logout
const rootReducer = (state: AppState | undefined, action: AnyAction) => {
  if (action.type === "RESET") {
    state = undefined;
  }
  return appReducer(state, action);
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure the store with the persistedReducer and middleware
export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          "persist/PERSIST",
          "persist/REHYDRATE",
          // Add RTK Query specific actions
          `${MetaWithPersist.reducerPath}/executeQuery/fulfilled`,
          `${MetaWithPersist.reducerPath}/executeMutation/fulfilled`
          // Add similar entries for other API slices if needed
        ],
        // Optionally ignore specific paths in the state that may contain non-serializable values
        ignoredPaths: [
          `${MetaWithPersist.reducerPath}.queries`,
          `${MetaWithPersist.reducerPath}.mutations`
          // Add similar entries for other API slices if needed
        ]
      }
    }).concat(
      Auth.middleware,
      Supplier.middleware,
      Retailer.middleware,
      Meta.middleware,
      MetaWithPersist.middleware,
      RetailerProduct.middleware,
      Product.middleware,
      Order.middleware,
      SupplierDashboard.middleware,
      Conversation.middleware
    ),
  devTools: process.env.NODE_ENV !== "production"
});

// Define RootState and AppDispatch types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type AppState = ReturnType<typeof appReducer>;

// Set up listeners for refetchOnFocus/refetchOnReconnect behaviors
setupListeners(store.dispatch);
export const persistor = persistStore(store);
