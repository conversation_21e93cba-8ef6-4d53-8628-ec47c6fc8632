import { STORE_ID } from "@/constants/cookies";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import Cookies from "js-cookie";
import { IntegrationDataItem } from "../retailer/types";

export interface RetailerStoreState {
  selectedRetailerStoreId: string | null;
  data: IntegrationDataItem | null;
  isError: boolean;
  isLoading: boolean;
}

const initialState: RetailerStoreState = {
  selectedRetailerStoreId: null,
  data: null,
  isError: false,
  isLoading: false
};

export const retailerStoreSlice = createSlice({
  name: "retailerStore",
  initialState,
  reducers: {
    setRetailerStoreId: (state, action: PayloadAction<string>) => {
      state.selectedRetailerStoreId = action.payload;
      Cookies.set(STORE_ID, action.payload);
    },
    getRetailerStoreId: state => {
      const id = Cookies.get(STORE_ID);
      if (id) {
        state.selectedRetailerStoreId = id;
      }
    },
    setRetailerStoreData: (state, action: PayloadAction<IntegrationDataItem | null>) => {
      state.data = action.payload;
    },
    setIsError: (state, action: PayloadAction<boolean>) => {
      state.isError = action.payload;
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    }
  }
});

export const { setRetailerStoreId, getRetailerStoreId, setRetailerStoreData, setIsError, setIsLoading } =
  retailerStoreSlice.actions;
export default retailerStoreSlice.reducer;
