import useModalStore from "@/store/zustand/modalStore";
import i18next from "i18next";
import i18n from "../i18n";
import { RETAILER_USER_TYPE } from "@/constants";
import { AxiosError } from "axios";
import useSessionStore from "@/store/zustand/sessionStore";
import { ApiError } from "./apiService";
import StoreModalBody from "@/app/retailer/(dashboard)/store/StoreWarningModalBody";

type ModalProps = {
  icon: string;
  title: string;
  subTitle: string;
  actions: Array<{ label: string; onClick: () => void }>;
};

const openErrorModal = (modalProps: ModalProps) => {
  if (typeof window === "undefined") return;
  useModalStore?.getState()?.openModal(modalProps);
};

export const handleError = async (error: any) => {
  // const cookieStore = cookies();
  // const sessionCookie = cookieStore.get(SESSION_DATA)?.value;
  // const sessionData: SessionData | null = !!sessionCookie ? JSON.parse(sessionCookie) : null;
  const sessionData = useSessionStore?.getState();
  const user_type = sessionData?.user_type;

  const isRetailer = user_type === RETAILER_USER_TYPE;
  // const { response, message } = error;

  const message = error?.message || error?.response?.message || error?.data?.error || error?.data;
  const status = error?.response?.status || error?.status;

  //   if (response?.data && (!response.data.error_detail || response.status !== 400)) {
  //     clientDefaultErrorHandler(response.data);
  //   }

  const isMobile = typeof window !== undefined && window.innerWidth < 768;

  if (status === 424 && isRetailer) {
    useModalStore.getState()?.openModal({
      body: (
        <StoreModalBody
          title={i18next.t("store.disconnected.title")}
          subtitle={i18next.t("store.disconnected.subtitle")}
          buttonText={i18next.t("confirm")}
        />
      ),
      width: isMobile ? undefined : 428
    });
  } else if (status === 417 && isRetailer) {
    if (typeof window === "undefined") return;
    useModalStore.getState()?.openModal({
      body: (
        <StoreModalBody
          title={i18next.t("store.notExistStore.title")}
          subtitle={i18next.t("store.notExistStore.subtitle")}
          buttonText={i18next.t("confirm")}
        />
      ),
      width: isMobile ? undefined : 428
    });
  } else if (status === 413) {
    openErrorModal({
      icon: "/images/svgs/danger.svg",
      title: i18next.t("errorTitle"),
      subTitle: i18next.t("globalErrorMessage"),
      actions: [
        {
          label: i18next.t("confirm"),
          onClick: () => useModalStore?.getState()?.closeModal()
        }
      ]
    });
  } else if (message) {
    const errorMessage = message;
    const translatedMessage =
      i18next.t("serverErrors." + errorMessage) === "serverErrors." + errorMessage
        ? i18next.t("serverErrors." + errorMessage)
        : i18next.t("globalErrorMessage");

    openErrorModal({
      icon: "/images/svgs/danger.svg",
      title: i18next.t("errorTitle"),
      subTitle: translatedMessage,
      actions: [
        {
          label: i18next.t("confirm"),
          onClick: () => useModalStore?.getState()?.closeModal()
        }
      ]
    });
  } else if (error?.response?.data?.error) {
    openErrorModal({
      icon: "/images/svgs/danger.svg",
      title: i18next.t("errorTitle"),
      subTitle: i18next.t("globalErrorMessage"),
      actions: [
        {
          label: i18next.t("confirm"),
          onClick: () => useModalStore?.getState()?.closeModal()
        }
      ]
    });
  } else if (message === "Network Error") {
    openErrorModal({
      icon: "/images/svgs/danger.svg",
      title: i18next.t("networkerror.title"),
      subTitle: i18next.t("networkerror.subtitle"),
      actions: [
        {
          label: i18next.t("confirm"),
          onClick: () => useModalStore?.getState()?.closeModal()
        }
      ]
    });
  }
};

export type SetHookFormError = (
  f: string,
  error: { message: string; type: "validation" | "required" | "custom" }
) => void;

export type ClientDefaultErrorHandler = {
  bodyError?: ApiError;
  error?: AxiosError<ApiError>;
  setFieldError?: (f: string, m: string) => void;
  setHookFormFieldError?: SetHookFormError;
};

export const clientDefaultErrorHandler = ({
  bodyError,
  setFieldError,
  setHookFormFieldError,
  error
}: ClientDefaultErrorHandler) => {
  if (!!Object.keys(bodyError?.error_detail || {})?.length) {
    Object.entries(bodyError?.error_detail || {}).forEach(([key, value]) => {
      let fieldMessages = "";
      const messageKeys = (value as [string, string])[0].split(/[.|:]/);
      const errorKey = key.split(/[.|:]/)?.[0];

      messageKeys.forEach((messageKey: string) => {
        // some messages are with value like minLength:1
        const hasValue = messageKeys?.[1] || false;
        const trKey = "serverErrors.fields." + (hasValue ? messageKey.split(":")[0] : messageKey);
        const translation = i18n.t(trKey, { value: hasValue });

        if (translation !== trKey) {
          if (setFieldError || setHookFormFieldError) {
            fieldMessages += "\n" + translation;
          } else {
            useModalStore?.getState()?.openModal({
              icon: "/images/svgs/danger.svg",
              title: i18n.t("errorTitle"),
              subTitle: errorKey + ": " + translation,
              actions: [
                {
                  label: i18n.t("confirm"),
                  onClick: () => useModalStore?.getState()?.closeModal()
                }
              ]
            });
          }
        }
      });

      if (fieldMessages) {
        if (setHookFormFieldError) {
          setHookFormFieldError(errorKey, {
            message: fieldMessages.trim(),
            type: "validation"
          });
        } else if (setFieldError) {
          setFieldError(errorKey, fieldMessages.trim());
        }
      }
    });
  } else {
    handleError(error);
  }
};
