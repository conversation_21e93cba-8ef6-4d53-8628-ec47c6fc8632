import i18n from "@/utils/i18n";
import { parseISO, subYears } from "date-fns";
import * as yup from "yup";

import "yup-phone";

const phoneSchema = yup.string().phone().required(i18n.t("retailer.profile.validations.requiredField"));

yup.addMethod(yup.string, "phone", function (errorMessage: string) {
  return this.test("phone", errorMessage, value => (!value ? true : phoneSchema.isValidSync(value)));
});

const validateIranianNationalId = (value: string) => {
  const pattern = /^\d{10}$/;

  if (!pattern.test(value)) {
    return false; // ID code must be 10 digits long
  }

  const check = parseInt(value[9]);
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(value[i]) * (10 - i);
  }
  sum %= 11;

  return (sum < 2 && check === sum) || (sum >= 2 && check + sum === 11);
};

export const retailerInfoValidationSchema = ({ isEdit }: { isEdit: boolean }) =>
  new yup.ObjectSchema({
    contactEmail: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup
          .string()
          .email(i18n.t("retailer.profile.validations.invalidEmail"))
          .required(i18n.t("retailer.profile.validations.requiredField"));
      }
      return schema;
    }),
    legalContactEmail: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .email(i18n.t("retailer.profile.validations.invalidEmail"))
          .required(i18n.t("retailer.profile.validations.requiredField"));
      }
      return schema;
    }),
    contactNumber: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup.string().required(i18n.t("retailer.profile.validations.requiredField"));
      }
      return schema;
    }),
    legalContactNumber: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup.string().required(i18n.t("retailer.profile.validations.requiredField"));
      }
      return schema;
    }),
    name: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("retailer.profile.validations.requiredField"))
          .min(3, i18n.t("retailer.profile.validations.min3"))
          .max(32, i18n.t("retailer.profile.validations.max32"));
      }
      return schema;
    }),
    legalName: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("retailer.profile.validations.requiredField"))
          .min(3, i18n.t("retailer.profile.validations.min3"))
          .max(32, i18n.t("retailer.profile.validations.max32"));
      }
      return schema;
    }),
    vatNumber: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup.string().required(i18n.t("retailer.profile.validations.requiredField"));
      }
      return schema;
    }),
    isLegalPerson: yup.boolean(),
    nationalCode: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("retailer.profile.validations.requiredField"))
          .matches(/^\d{10}$/, i18n.t("retailer.profile.validations.nationalCode"))
          .test("nationalCode", i18n.t("retailer.profile.validations.nationalCode"), value =>
            validateIranianNationalId(value as string)
          );
      }
      return schema;
    }),
    birthDay: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("retailer.profile.validations.requiredField"))
          .test("is-18", i18n.t("retailer.profile.validations.birthDay"), value => {
            if (!value) return false;
            const date = parseISO(value);
            return subYears(new Date(), 18) >= date;
          });
      }
      return schema;
    }),
    companyName: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("retailer.profile.validations.requiredField"))
          .min(3, i18n.t("retailer.profile.validations.min3"))
          .max(64, i18n.t("retailer.profile.validations.max64"));
      }
      return schema;
    }),
    companyType: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("retailer.profile.validations.requiredField"))
          .min(3, i18n.t("validations.min", { number: 3 }))
          .max(32, i18n.t("validations.max", { number: 32 }));
      }
      return schema;
    }),
    economicCode: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .min(3, i18n.t("validations.min", { number: 3 }))
          .max(32, i18n.t("validations.max", { number: 32 }))
          .required(i18n.t("retailer.profile.validations.requiredField"));
      }
      return schema;
    }),
    registrationNumber: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .min(3, i18n.t("validations.min", { number: 3 }))
          .max(32, i18n.t("validations.max", { number: 32 }))
          .required(i18n.t("retailer.profile.validations.requiredField"));
      }
      return schema;
    }),
    bankAccount: yup.object().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup.object({
          bic: yup.string(),
          holderName: yup
            .string()
            .required(i18n.t("retailer.profile.validations.requiredField"))
            .min(3, i18n.t("retailer.profile.validations.min3"))
            .max(32, i18n.t("retailer.profile.validations.max32")),
          iban: yup
            .string()
            .required(i18n.t("retailer.profile.validations.requiredField"))
            .matches(/^[0-9]{24}$/, i18n.t("retailer.profile.validations.iban"))
        });
      }
      return schema;
    }),
    legalBankAccount: yup.object().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup.object({
          bic: yup.string(),
          holderName: yup
            .string()
            .required(i18n.t("retailer.profile.validations.requiredField"))
            .min(3, i18n.t("retailer.profile.validations.min3"))
            .max(32, i18n.t("retailer.profile.validations.max32")),
          iban: yup
            .string()
            .required(i18n.t("retailer.profile.validations.requiredField"))
            .matches(/^[0-9]{24}$/, i18n.t("retailer.profile.validations.iban"))
        });
      }
      return schema;
    }),
    markupType: yup.string().when([], schema => {
      if (isEdit) {
        return yup
          .string()
          .oneOf(["Fixed", "Percentage"], i18n.t("retailer.profile.validations.invalidMarkupType"))
          .required(i18n.t("retailer.profile.validations.requiredField"));
      }
      return yup.string().notRequired();
    }),
    markup: yup
    .number()
    .transform((value, originalValue) =>
      originalValue === "" || isNaN(value) ? undefined : value
    )
    .typeError(i18n.t("retailer.profile.validations.mustBeNumber"))
    .when("markupType", {
      is: "Fixed",
      then: schema =>
        schema
          .min(0, i18n.t("retailer.profile.validations.mustBeAtLeastZero"))
          .required(i18n.t("retailer.profile.validations.requiredField")),
      otherwise: schema =>
        schema
          .min(0, i18n.t("retailer.profile.validations.mustBeAtLeastZero"))
          .max(100, i18n.t("retailer.profile.validations.mustNotExceed100"))
          .required(i18n.t("retailer.profile.validations.requiredField"))
    }),
    documents: yup
      .object()
      .shape({
        "legal-national-front": yup.array().nullable(),
        "legal-national-back": yup.array().nullable(),
        "legal-newspaper": yup.array().nullable(),
        "legal-certificate-of-added-value": yup.array().nullable(),
        "national-front": yup.array().nullable(),
        "national-back": yup.array().nullable()
      })
      .when([], (documents, { parent: context }) => {
        if (context?.isLegalPerson) {
          return yup.object().shape({
            "legal-national-front": yup.array().required(i18n.t("retailer.profile.validations.requiredField")),
            "legal-national-back": yup.array().required(i18n.t("retailer.profile.validations.requiredField")),
            "legal-newspaper": yup.array().required(i18n.t("retailer.profile.validations.requiredField")),
            "legal-certificate-of-added-value": yup
              .array()
              .required(i18n.t("retailer.profile.validations.requiredField"))
          });
        } else {
          return yup.object().shape({
            "national-front": yup.array().required(i18n.t("retailer.profile.validations.requiredField")),
            "national-back": yup.array().required(i18n.t("retailer.profile.validations.requiredField"))
          });
        }
      })
  });

export const retailerAddressValidation = yup.object({
  address: yup.object({
    address1: yup
      .string()
      .required(i18n.t("retailer.profile.validations.requiredField"))
      .min(5, i18n.t("retailer.profile.validations.min5")),
    locationId: yup.string().required(i18n.t("retailer.profile.validations.requiredField")),
    zip: yup
      .string()
      .matches(/^\d{10}$/gm, i18n.t("errors.zip"))
      .required(i18n.t("retailer.profile.validations.requiredField"))
  })
});
