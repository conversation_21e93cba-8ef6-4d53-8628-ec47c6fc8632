import BottomAction from "@/components/ui/bottomAction/BottomAction";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";
import Input from "@/components/ui/inputs/Input";
import NumberInput from "@/components/ui/inputs/NumberInput";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";
import { usePutOrderShippingAddressMutation } from "@/store/apps/order";
import { TOrderData, TOrderShippingAddressBody } from "@/store/apps/order/types";
import { isValidUUID, SnakeToCamelFieldErrorWrapper } from "@/utils/helpers";
import useModal from "@/utils/hooks/useModal";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { orderShippingAddressValidation } from "@/utils/validations/profile/supplier";
import { CircularProgress, Grid } from "@mui/material";
import { FormikHelpers, useFormik } from "formik";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Router } from "next/router";
import { useTranslation } from "react-i18next";

interface IOrderShippingAddressProps {
  shippingAddress: TOrderData["shippingAddress"];
  oid?: string;
  hasUncalculatedShipping?: boolean;
}

function OrderShippingAddress({ shippingAddress, oid, hasUncalculatedShipping }: IOrderShippingAddressProps) {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const router = useRouter();

  const [putOrderShippingAddress, { isLoading }] = usePutOrderShippingAddressMutation();

  const initialValues = !hasUncalculatedShipping
    ? {
        zip: shippingAddress?.zip || "",
        locationId:
          shippingAddress?.locationId && isValidUUID(shippingAddress?.locationId) ? shippingAddress?.locationId : "",
        address1: shippingAddress?.address1 || ""
      }
    : {
        zip: "",
        locationId: "",
        address1: ""
      };

  const onSubmit = async (
    values: TOrderShippingAddressBody["body"],
    { setFieldError }: FormikHelpers<typeof initialValues>
  ) => {
    const body = {
      zip: values?.zip,
      locationId: values?.locationId,
      address1: values?.address1
    };
    if (!oid) return;

    try {
      await putOrderShippingAddress({ body, oid }).then(res => {
        const error = (res as any)?.error?.data;

        if (error) {
          clientDefaultErrorHandler({
            error: (res as any)?.error,
            bodyError: error,
            setFieldError: SnakeToCamelFieldErrorWrapper(setFieldError)
          });
        }
        if ("data" in res && res?.data) {
          hideModal();
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema: orderShippingAddressValidation,
    onSubmit
  });

  const { values, handleChange, handleBlur, errors, touched, setFieldValue } = formik;

  return (
    <>
      {/* for Desktop */}
      <div className="xmd:block hidden">
        <div className="flex items-center gap-2 absolute top-7 right-6">
          <Image src="/images/svgs/orderShippingAddress.svg" alt="editAddress" width={24} height={24} />
          <span className=" text-gray-600 text-body3-medium">{t("order.editAddressTitle")}</span>
        </div>
        <div className="flex flex-col mt-6 gap-2">
          <span className="text-caption-regular text-gray-600">{t("order.enteredAddress")}</span>
          <span className="text-body2-medium text-gray-999">{shippingAddress?.address1 || "-"}</span>
          <div className="flex items-center gap-1">
            <span className="text-body3-medium text-gray-500">{t("order.phone")} : </span>
            <span className="text-body3-medium text-gray-999">{shippingAddress?.phoneNumber || "-"}</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-body3-medium text-gray-500">{t("order.zipcode")} : </span>
            <span className="text-body3-medium text-gray-999">{shippingAddress?.zip || "-"}</span>
          </div>
        </div>
      </div>

      {/* for Mobile */}
      <div className="xmd:hidden block">
        <div className="flex flex-col mt-6 gap-2">
          <span className="text-body4-medium text-v2-content-tertiary">{t("order.enteredAddress")}</span>
          <span className="text-body3-medium text-v2-content-primary">{shippingAddress?.address1 || "-"}</span>
          <div className="flex items-center gap-1">
            <span className="text-body3-medium text-v2-content-tertiary">{t("order.phone")} : </span>
            <span className="text-body3-medium text-v2-content-primary">{shippingAddress?.phoneNumber || "-"}</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-body3-medium text-v2-content-tertiary">{t("order.zipcode")} : </span>
            <span className="text-body3-medium text-v2-content-primary">{shippingAddress?.zip || "-"}</span>
          </div>
        </div>
      </div>

      <form onSubmit={formik.handleSubmit} className="mt-6">
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <LocationsSelect
              multiple={false}
              label={t("order.editShipping.city")}
              placeholder={t("order.editShipping.cityPlaceholder")}
              handleBlur={handleBlur}
              value={values?.locationId}
              onChange={value => setFieldValue("locationId", value)}
              name="address.locationId"
              error={touched?.locationId && Boolean(errors?.locationId)}
              helperText={touched?.locationId && errors?.locationId}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <NumberInput
              id="address.zip"
              name="address.zip"
              value={values?.zip}
              label={t("order.editShipping.zip")}
              placeholder={t("order.editShipping.zipPlaceholder")}
              onChange={e => setFieldValue("zip", e?.target?.value)}
              onBlur={handleBlur}
              error={touched?.zip && Boolean(errors?.zip)}
              helperText={(touched?.zip && errors?.zip) || undefined}
            />
          </Grid>
          <Grid item xs={12} md={12}>
            <Textarea
              label={t("order.editShipping.address")}
              placeholder={t("order.editShipping.addressPlaceholder")}
              id="address1"
              name="address1"
              rows={4}
              value={values?.address1}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched?.address1 && Boolean(errors?.address1)}
              helperText={touched?.address1 && errors?.address1}
            />
          </Grid>

          <BottomAction
            saveButtonText={isLoading ? <CircularProgress size={16} /> : t("confirm")}
            saveButtonProps={{
              type: "submit"
            }}
            cancelButtonText={t("order.cancel")}
            cancelButtonProps={{
              onClick: () => router.back()
            }}
          />

          <Grid item xs={12} md={12}>
            <div className="w-full hidden xmd:flex gap-4 mt-4">
              <CustomButton fullWidth color="secondary" onClick={() => hideModal()}>
                {t("order.cancel")}
              </CustomButton>
              <CustomButton fullWidth type="submit" disabled={isLoading}>
                {isLoading ? <CircularProgress size={16} /> : t("confirm")}
              </CustomButton>
            </div>
          </Grid>
        </Grid>
      </form>
    </>
  );
}

export default OrderShippingAddress;
