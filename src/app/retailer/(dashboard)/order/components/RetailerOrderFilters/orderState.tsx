import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import "swiper/swiper-bundle.css";
import { useFilters } from "./useFilters";

interface IOrderStateFilterProps {
  totalCount: number;
}

const OrderStateFilter = ({ totalCount }: IOrderStateFilterProps) => {
  const { t } = useTranslation();
  const { setFilters, filters } = useFilters();

  const stateItems = [
    {
      id: "All",
      label: t("supplierOrder.orderStateItems.all")
    },
    {
      id: "Pending",
      label: t("supplierOrder.orderStateItems.pending")
    },
    {
      id: "InProgress",
      label: t("supplierOrder.orderStateItems.inprogress")
    },
    {
      id: "Done",
      label: t("supplierOrder.orderStateItems.done")
    },
    {
      id: "Refunded",
      label: t("supplierOrder.orderStateItems.refunded")
    },
    {
      id: "Canceled",
      label: t("supplierOrder.orderStateItems.canceled")
    }
  ];

  return (
    <div>
      <Swiper
        dir="rtl"
        slidesPerView={"auto"}
        keyboard
        grabCursor
        spaceBetween={24}
        modules={[Navigation, Keyboard, Mousewheel]}
      >
        {stateItems?.map((item, index) => (
          <SwiperSlide key={item?.id} className="max-w-fit">
            <div
              key={item?.id}
              onClick={() => {
                if (item?.id === "All") {
                  setFilters({ state: null }, { history: "push" });
                  return;
                }
                setFilters({ state: item?.id as any }, { history: "push" });
              }}
              className={twMerge(
                "whitespace-nowrap border-b-transparent border-b-2 pb-2.5 flex items-center gap-1 cursor-pointer",
                (filters?.state || stateItems[0]?.id) === item?.id
                  ? "text-purple-500 !border-b-v2-content-on-action-2"
                  : ""
              )}
            >
              <span className="text-body3-medium text-v2-content-primary">{item?.label}</span>
              <div className="bg-v2-surface-action-light text-body3-medium px-1.5 text-v2-content-on-action-hover-2 rounded-[4px] shrink-0 ">
                {item?.id === "All" ? totalCount : 0}
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default OrderStateFilter;
