"use client";

import "./RetailerOrderTable.css";
import React, { useState } from "react";
import { Box, Divider, SelectChangeEvent } from "@mui/material";
import { TableCell } from "@mui/material";
import { TableHead } from "@mui/material";
import { TableRow } from "@mui/material";
import { TableSortLabel } from "@mui/material";
import { Typography } from "@mui/material";
import { visuallyHidden } from "@mui/utils";
import { useTranslation } from "react-i18next";
import i18n from "@/utils/i18n";
import Link from "next/link";
import { EnhancedTableProps, HeadCell, Order } from "./types";
import useLanguage from "@/utils/hooks/useLanguage";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import useCurrency from "@/utils/hooks/useCurrency";
import { useGetOrderListQuery } from "@/store/apps/order";
import { TOrderData, TOrderLineItem } from "@/store/apps/order/types";
import CustomTablePagination from "@/components/ui/CustomTablePagination/CustomTablePagination";
import { Icon } from "@iconify/react";
import RetailerOrderTableMobile from "./RetailerOrderTableMobile";
import RetailerOrderFilters from "./RetailerOrderFilters/RetailerOrderFilters";
import useModal from "@/utils/hooks/useModal";
import RemoveOrder from "./RemoveOrder";
import clsx from "clsx";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import LoadingList from "@/components/containers/LoadingList/LoadingList";
import { generateBackendFilters, generateBackendSorts } from "@/utils/services/transformers";
import useLocations from "@/utils/hooks/useLocations";
import Image from "next/image";
import OrderStatus, { IOrderStatusItems } from "@/app/supplier/(dashboard)/order/[id]/orderDetail/OrderStatus";
import OrdersEmptyList from "@/app/supplier/(dashboard)/order/components/OrdersEmptyList";
import { useFilters } from "./RetailerOrderFilters/useFilters";
import { omitEmptyValues } from "@/utils/helpers";
import OrderStateFilter from "./RetailerOrderFilters/orderState";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import Button from "@/components/ui/Button";

const headCells: readonly HeadCell[] = [
  {
    id: "cover",
    numeric: false,
    disablePadding: false,
    label: i18n.t("order.photo")
  },
  {
    id: "title",
    numeric: false,
    disablePadding: false,
    label: i18n.t("order.title")
  },
  {
    id: "quantity",
    numeric: false,
    disablePadding: false,
    label: i18n.t("order.quantity")
  },
  {
    id: "totalPrice",
    numeric: false,
    disablePadding: false,
    label: i18n.t("order.totalPrice")
  },
  {
    id: "orderState",
    numeric: false,
    disablePadding: false,
    label: i18n.t("order.orderState")
  },

  {
    id: "supplier",
    numeric: false,
    disablePadding: false,
    label: i18n.t("order.supplier")
  },
  {
    id: "action",
    numeric: false,
    disablePadding: false,
    label: i18n.t("order.action")
  }
];

function EnhancedTableHead(props: EnhancedTableProps) {
  const { order, orderBy, onRequestSort } = props;
  const createSortHandler = (property: any) => (event: React.MouseEvent<unknown>) => {
    onRequestSort(event, property);
  };

  return (
    <TableHead className="bg-gray-20 text-body4-medium text-gray-600 sticky top-0 z-10 ">
      <TableRow className="supplier-order-table-header-item-table-row">
        {headCells.map((headCell, index) => (
          <TableCell
            key={headCell.id}
            padding={headCell.disablePadding ? "none" : "normal"}
            sortDirection={orderBy === headCell.id ? order : false}
            className={clsx("py-4", index === 0 ? "ps-3" : "")}
          >
            {headCell.hasSort ? (
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={orderBy === headCell.id ? order : "asc"}
                onClick={createSortHandler(headCell.id)}
              >
                <Typography whiteSpace="nowrap" className="supplier-order-table-header-item" width={150}>
                  {headCell.label}
                </Typography>
                {orderBy === headCell.id ? (
                  <Box component="span" sx={visuallyHidden}>
                    {order === "desc" ? "sorted descending" : "sorted ascending"}
                  </Box>
                ) : null}
              </TableSortLabel>
            ) : (
              <>
                <Typography whiteSpace="nowrap" className="supplier-order-table-header-item">
                  {headCell.label}
                </Typography>
                {orderBy === headCell.id ? (
                  <Box component="span" sx={visuallyHidden}>
                    {order === "desc" ? "sorted descending" : "sorted ascending"}
                  </Box>
                ) : null}
              </>
            )}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

const RetailerOrderTable = () => {
  const [selected, setSelected] = React.useState<Array<string | number>>([]);
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const [curr] = useCurrency();
  const [{ renderDate }] = useLanguage();
  const { render: renderPrice } = curr ?? { render: v => v };
  const { showModal } = useModal();
  const [loading] = useState(false);
  const { getLocation } = useLocations();
  const { filters, pagination, sorts: sortsStates, setFilters } = useFilters();
  const { page, pageSize } = pagination || {};
  const { created_at, updated_at } = sortsStates || {};
  const finalFilters = generateBackendFilters(omitEmptyValues(filters));
  const sorts = generateBackendSorts(omitEmptyValues({ created_at, updated_at }));
  const [downloadType, setDownloadType] = useState<"factor" | "label" | undefined>(undefined);

  const [checkedIds, setCheckedIds] = useState<string[]>([]);

  const handleCheckAllItems = (checked: boolean) => {
    const ids = orders?.data
      // ?.filter(item => !checkedIds?.find(v => v === item?.id))
      ?.map(item => item.id) as string[];

    setCheckedIds(checked ? ids : []);
  };

  const handleCheckItem = (id: string) => {
    const existId = checkedIds?.find(item => item === id);

    if (existId) {
      setCheckedIds(prev => prev?.filter(item => item !== id));
    } else {
      setCheckedIds(prev => [...prev, id]);
    }
  };

  const queryParts = [
    pageSize ? `page_size=${pageSize}` : "",
    page ? `page=${page}` : "",
    finalFilters ? `filters=${finalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ].filter(part => part !== "");
  const queryString = queryParts.join("&");

  const {
    data: orders,
    isLoading: isRetailerOrderLoading,
    isError: isRetailerOrderError,
    isFetching: isRetailerOrderFetching
  } = useGetOrderListQuery(queryString, { refetchOnMountOrArgChange: true });

  const isLoading = isRetailerOrderLoading || loading;

  // This is for select all the row
  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelecteds = orders?.data?.map?.((n: any) => n.id);
      if (newSelecteds) setSelected(newSelecteds);

      return;
    }
    setSelected([]);
  };

  // This is for the single row sleect
  const handleClick = (event: React.MouseEvent<unknown>, id: string | number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: Array<string | number> = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));
    }

    setSelected(newSelected);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setFilters({ page: newPage }, { history: "push" });
  };

  const handleChangeRowsPerPage = (event: SelectChangeEvent<number>) => {
    setFilters({ page: 1, pageSize: event.target.value as number }, { history: "push" });
  };

  // const emptyRows = rowsPerPage - (orders?.data?.length || 0);

  const totalCount = orders?.pagination?.total ?? 0;

  const handleDeleteOrder = ({ oid, vid }: { oid: string; vid: string }) => {
    showModal({
      icon: "/images/svgs/removeOrder.svg",
      body: <RemoveOrder oid={oid} vid={vid} />
    });
  };

  const RenderRemoveIcon = ({ row, lineItem }: { row: TOrderData; lineItem: TOrderLineItem }) => {
    if (row?.paymentState === "Pending" && lineItem?.state === "Pending")
      return (
        <Icon
          icon="solar:close-circle-linear"
          width={16}
          height={16}
          onClick={() => handleDeleteOrder({ oid: lineItem?.orderId, vid: lineItem?.variantId })}
          className={clsx("cursor-pointer")}
        />
      );
    return null;
  };

  return (
    <>
      <RetailerOrderTableMobile
        page={page}
        setPage={val => {
          setFilters({ page: val }, { history: "push" });
        }}
        pageSize={pageSize}
        rowsPerPage={pageSize}
        totalCount={totalCount}
        isLoading={isLoading}
        isRetailerOrderError={isRetailerOrderError}
        ordersData={orders?.data}
        RenderRemoveIcon={RenderRemoveIcon}
      />

      <CustomCardContent className="supplier-order-table-container">
        <>
          <div className=" items-center gap-2  hidden xmd:flex">
            {downloadType === "factor" ? (
              <div className="flex items-center gap-2 cursor-pointer" onClick={() => setDownloadType(undefined)}>
                <Icon icon="tabler:arrow-right" className="size-6" />
                <span className="text-body1-medium !font-semibold text-v2-content-primary">
                  {t("order.downloadFactorGroup")}
                </span>
              </div>
            ) : downloadType === "label" ? (
              <div className="flex items-center gap-2 cursor-pointer" onClick={() => setDownloadType(undefined)}>
                <Icon icon="tabler:arrow-right" className="size-6" />
                <span className="text-body1-medium !font-semibold text-v2-content-primary">
                  {t("order.downloadLabelGroup")}
                </span>
              </div>
            ) : (
              <>
                <h2 className="text-body1-medium text-gray-999  ">{t("orderList")}</h2>
                {!!orders?.pagination?.total && orders?.pagination?.total > 0 && (
                  <span className="text-v2-content-tertiary text-body1-medium">({orders?.pagination?.total})</span>
                )}
              </>
            )}
          </div>

          <div className="mt-6 mb-4 border-b border-b-v2-surface-action-light">
            <OrderStateFilter totalCount={totalCount} />
          </div>

          <RetailerOrderFilters
            RenderEndAdornment={
              !downloadType ? (
                <div className="items-center gap-2 xmd:flex hidden">
                  <Button
                    variant="tertiaryGray"
                    size="sm"
                    startAdornment={<Icon icon="solar:printer-outline" className="size-4" />}
                    onClick={() => setDownloadType("factor")}
                  >
                    {t("supplierOrder.printGroupFactor")}
                  </Button>

                  {/* <div className="w-px h-4 bg-v2-border-primary" />

                  <Button
                    variant="tertiaryGray"
                    size="sm"
                    startAdornment={<Icon icon="solar:printer-outline" className="size-4" />}
                    onClick={() => setDownloadType("label")}
                  >
                    {t("supplierOrder.printGroupLabel")}
                  </Button> */}
                </div>
              ) : undefined
            }
          />

          {isLoading || isRetailerOrderFetching ? (
            <LoadingList rowCount={6} columnCount={6} />
          ) : !orders?.data?.length ? (
            <Box width="100%" minHeight="50vh" display="flex" justifyContent="center" alignItems="center">
              <OrdersEmptyList />
            </Box>
          ) : isRetailerOrderError ? (
            <Box width="100%" minHeight="50vh" display="flex" justifyContent="center" alignItems="center">
              <Typography>{t("errors.somethingWentWrong")}</Typography>
            </Box>
          ) : (
            <>
              {!!orders?.data?.length && (
                <div className="flex flex-col min-h-[50vh] h-full gap-3 mt-5 ">
                  <div className="flex items-center justify-between mb-5">
                    {!!downloadType && (
                      <div className="flex items-center gap-2">
                        <CustomCheckbox
                          className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
                          indeterminate={!!checkedIds?.length && checkedIds?.length !== orders?.data?.length}
                          onChange={(_e, checked) => handleCheckAllItems(checked)}
                          checked={!!checkedIds?.length && checkedIds?.length === orders?.data?.length}
                        />

                        <span className="text-v2-content-primary text-body3-medium whitespace-nowrap">
                          {t("selectAll")}
                        </span>

                        {!!checkedIds?.length && (
                          <span className="text-body3-medium text-v2-content-tertiary whitespace-nowrap">
                            ({t("selected")} {checkedIds?.length})
                          </span>
                        )}
                      </div>
                    )}

                    {downloadType === "factor" &&
                      !!checkedIds?.length &&
                      checkedIds?.length === orders?.data?.length && (
                        <div className="rounded-lg p-2 bg-v2-surface-info flex items-center gap-1.5">
                          <Icon icon="gg:check-r" className="size-4 text-v2-content-tertiary" />
                          <span className="text-v2-content-tertiary text-body3-regular">
                            {t("order.downloadFactor.itemSelected", { number: checkedIds?.length })}
                          </span>
                          <span className="text-v2-content-on-info text-body3-medium">
                            {t("order.downloadFactor.selectAll", { number: orders?.pagination?.total })}
                          </span>
                        </div>
                      )}

                    {downloadType && (
                      <div>
                        {!!checkedIds?.length ? (
                          <div className="flex justify-between items-center ">
                            <Button
                              variant="secondaryColor"
                              startAdornment={
                                <Icon icon="solar:download-square-outline" className="size-5 text-v2-content-on-info" />
                              }
                            >
                              {downloadType === "factor" ? t("order.downloadFactors") : t("order.downloadLabels")}
                            </Button>
                          </div>
                        ) : (
                          <div className="h-10 w-0 opacity-0" />
                        )}
                      </div>
                    )}
                  </div>

                  {orders?.data?.map?.((row, index) => {
                    const labelId = `enhanced-table-checkbox-${index}-${row.id}`;

                    const lineItemImageCount = row?.lineItems?.filter(i => i?.product?.cover?.url)?.length;

                    return (
                      <div className="flex items-start gap-2" key={row?.id}>
                        {!!downloadType && (
                          <CustomCheckbox
                            className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
                            onChange={() => handleCheckItem(row.id)}
                            checked={!!checkedIds?.length && checkedIds?.some(v => v === row?.id)}
                          />
                        )}

                        <Link
                          href={`${makePath(routes.order)}/${row.id}`}
                          className=" border border-v2-border-primary p-4 rounded-[9px] flex-1"
                        >
                          <div className="flex items-center justify-between pb-6 border-b border-b-v2-border-primary">
                            <div className="flex items-center gap-3 ">
                              <div className="flex items-center gap-1">
                                <Icon
                                  icon="mingcute:square-arrow-left-fill"
                                  className="text-v2-content-primary size-4"
                                />
                                <span className="text-body3-medium text-v2-content-primary">
                                  {row?.orderNumber ? row?.orderNumber : "-"}
                                </span>
                              </div>

                              <Divider
                                orientation="vertical"
                                variant="middle"
                                flexItem
                                className=" border-v2-border-primary h-4"
                              />

                              {!!row?.createdAt && (
                                <div>
                                  <span className="text-body3-medium text-gray-999">
                                    {renderDate(row?.createdAt, "hh:mm - YYYY/MM/DD")}
                                  </span>
                                </div>
                              )}

                              {!!row.state && (
                                <div>
                                  <OrderStatus
                                    id={row.state as IOrderStatusItems["id"]}
                                    title={t(`supplierOrder.orderStateItems.${row.state?.toLowerCase()}`)}
                                  />
                                </div>
                              )}
                            </div>

                            <div className="flex items-center gap-2">
                              <div className="flex items-center gap-1">
                                {row?.lineItems?.slice(0, 3)?.map(lItem => (
                                  <>
                                    {!!lItem?.product?.cover?.url && (
                                      <div className="relative rounded-[4px] overflow-hidden ">
                                        <Image
                                          key={lItem?.id}
                                          src={lItem?.product?.cover?.url}
                                          alt={lItem?.product?.cover?.alt}
                                          width={25}
                                          height={25}
                                          style={{
                                            borderRadius: "4px"
                                          }}
                                        />
                                        {!!lineItemImageCount && lineItemImageCount >= 3 && (
                                          <div
                                            style={{
                                              background: "rgba(0, 0, 0, 0.4)"
                                            }}
                                            className="text-body4-medium absolute w-full h-full top-0 left-0 flex items-center justify-center"
                                          >
                                            <span className="text-v2-content-on-action-hover-1">
                                              {lineItemImageCount}+
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </>
                                ))}
                              </div>
                              <Link href={`${makePath(routes.order)}/${row.id}`}>
                                <Icon icon="solar:alt-arrow-left-outline" className="text-v2-content-primary size-4" />
                              </Link>
                            </div>
                          </div>

                          <div className="flex items-center justify-between pt-4">
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-1">
                                <Icon icon="hugeicons:user-circle-02" className="size-4" />
                                <span className="text-caption-medium text-v2-content-primary">
                                  {row?.customer?.firstName || "" + "" + row?.customer?.lastName || ""}
                                </span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Icon icon="solar:phone-calling-rounded-outline" className="size-4" />
                                <span className="text-caption-medium text-v2-content-primary">
                                  {row?.customer?.phoneNumber}
                                </span>
                              </div>
                              {row?.shippingAddress?.locationId && (
                                <div className="flex items-center gap-1">
                                  <Icon icon="solar:map-point-outline" className="size-4" />
                                  {row?.hasUncalculatedShipping ? (
                                    <div className="flex items-center gap-1">
                                      <span className="text-v2-content-on-warning-1 text-caption-medium">
                                        {t("order.notVerifiedAddress")}
                                      </span>
                                      <Icon icon="solar:danger-bold" className="text-v2-content-on-warning-1" />
                                    </div>
                                  ) : (
                                    <span className="text-caption-medium text-v2-content-primary">
                                      {getLocation(row?.shippingAddress?.locationId)?.name ?? "-"}
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-1">
                              <Icon icon="solar:bill-check-outline" className="text-v2-content-tertiary" />
                              <span className="text-body4-medium text-v2-content-primary ">
                                {t("order.totalPrice")} :
                              </span>
                              <span className="text-caption-medium text-v2-content-primary ">
                                {renderPrice(row.totalPrice || "0")}
                              </span>
                            </div>
                          </div>
                        </Link>
                      </div>
                    );
                  })}

                  {totalCount > 10 && (
                    <div className="mt-auto pt-4 border-t border-t-v2-border-primary">
                      <CustomTablePagination
                        rowsPerPageOptions={[10, 50, 100, 200]}
                        count={totalCount}
                        rowsPerPage={pageSize}
                        page={page}
                        onPageChange={handleChangePage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                        labelRowsPerPage={t("product.rowPerPage")}
                      />
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </>
      </CustomCardContent>
    </>
  );
};

export default RetailerOrderTable;
