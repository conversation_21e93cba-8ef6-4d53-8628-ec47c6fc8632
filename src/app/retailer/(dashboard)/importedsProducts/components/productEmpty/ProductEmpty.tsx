import AddSupplierStore from "@/components/containers/SupplierStoreSetting/AddSupplierStore";
import Button from "@/components/ui/Button";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Typography } from "@mui/material";
import { isDraft } from "@reduxjs/toolkit";
import { IconPlus } from "@tabler/icons-react";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";

interface IProductEmptyProps {
  isDraft: boolean;
  hasSearchValue: boolean;
}

function ProductEmpty({ isDraft, hasSearchValue }: IProductEmptyProps) {
  const makePath = useRoleBasePath();
  const { t } = useTranslation();

  if (hasSearchValue) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh] gap-3 bg-cards rounded-lg">
        <div className="w-[85px] h-[100px] xmd:w-[196px] xmd:h-[230px] relative">
          <Image src="/images/product-list-empty-search.svg" fill alt="empty list palceholder" />
        </div>

        <div className="flex gap-2 flex-col text-center">
          <span className="text-h5-bold !font-semibold text-v2-content-primary">
            {t("retailer.product.emptySearchTitle")}
          </span>
          <span className="text-body3-medium text-v2-content-tertiary">
            {t("retailer.product.emptySearchSubTitle")}{" "}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center h-[70vh] gap-5 bg-cards rounded-lg">
      <div className="w-[85px] h-[100px] xmd:w-[196px] xmd:h-[230px] relative">
        <Image src="/images/product-list-empty-2.svg" fill alt="empty list palceholder" />
      </div>

      <div className="flex gap-2 flex-col text-center">
        <span className="text-h5-bold !font-semibold text-v2-content-primary">
          {isDraft ? t("retailer.product.emptyDraftTitle") : t("retailer.product.emptyImportsTitle")}
        </span>
        <span className="text-body3-medium text-v2-content-tertiary">
          {isDraft ? t("retailer.product.emptyDraftSubTitle") : t("retailer.product.emptyImportsSubTitle")}{" "}
        </span>
      </div>
      {isDraft && (
        <div className="flex flex-col gap-3">
          <Link href={makePath(routes.product)}>
            <Button size="lg" variant="secondaryColor">
              <IconPlus />
              {t("retailer.product.emptyDraftAddButton")}
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}

export default ProductEmpty;
