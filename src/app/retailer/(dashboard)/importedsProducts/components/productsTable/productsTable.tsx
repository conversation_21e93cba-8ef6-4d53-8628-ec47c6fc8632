"use client";

import React, { useMemo, useState } from "react";
import { Avatar, SelectChangeEvent, Stack, Theme, useMediaQuery } from "@mui/material";
import { Table } from "@mui/material";
import { TableBody } from "@mui/material";
import { TableCell } from "@mui/material";
import { TableHead } from "@mui/material";
import { TableRow } from "@mui/material";
import { routes } from "@/constants/routes";
import { useTranslation } from "react-i18next";
import { TableContainer } from "@mui/material";
import { Box } from "@mui/material";
import { CircularProgress } from "@mui/material";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import Link from "next/link";
import CustomTablePagination from "@/components/ui/CustomTablePagination/CustomTablePagination";
import { Icon } from "@iconify/react";
import useModal from "@/utils/hooks/useModal";
import { headerItems } from "./utils";
import Filters from "./Filters/Filters";
import {
  useDeleteRetailderProductMutation,
  useGetRetailerImportsListQuery,
  usePostRetailderProductPushMutation,
  usePostRetailderProductUnPushMutation
} from "@/store/apps/retailerProduct";
import { usePathname } from "next/navigation";
import ProductsTableMobile from "./productsTableMobile/productsTableMobile";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { generateBackendFilters, generateBackendSorts } from "@/utils/services/transformers";
import Button from "@/components/ui/Button";
import { useFiltersState } from "./Filters/useFiltersState";
import { omitEmptyValues } from "@/utils/helpers";
import ProductEmptyList from "@/components/containers/RetailerProduct/components/ProductEmptyList";
import { useSelector } from "@/store/hooks";
import { AppState } from "@/store/store";
import { shallowEqual } from "react-redux";

function ProductsTable() {
  /* ---------------------------------- hooks --------------------------------- */
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal, hideModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const pathname = usePathname();
  const { data: retailerStore } = useSelector(
    (state: AppState) => ({
      data: state?.retailerStore?.data
    }),
    shallowEqual
  );

  /* --------------------------------- states --------------------------------- */
  const { filters, setFilters } = useFiltersState();
  const { page, pageSize, created_at, updated_at, ...restFilters } = filters || {};
  const nonEmptyFilters = useMemo(() => omitEmptyValues(restFilters), [restFilters]);

  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const isDraft = pathname.includes(routes.retailerProductsDrafts);

  const finalFilters = generateBackendFilters({ ...nonEmptyFilters, draft: isDraft });
  const sorts = generateBackendSorts(
    omitEmptyValues({
      created_at: (created_at as any) || undefined,
      updated_at: (updated_at as any) || undefined
    })
  );

  const queryParts = [
    `page_size=${filters?.pageSize}`,
    `page=${filters?.page}`,
    finalFilters ? `filters=${finalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ].filter(part => part !== "");
  const queryString = queryParts.join("&");

  /* ----------------------------------- rtk ---------------------------------- */
  const {
    data: productData,
    isLoading: isProductLoading,
    isFetching: isProductFetching,
    isError: isProductError
  } = useGetRetailerImportsListQuery(queryString || "", {
    skip: !filters?.page,
    refetchOnMountOrArgChange: true
  });
  const [pushProducts] = usePostRetailderProductPushMutation();
  const [unpushProducts] = usePostRetailderProductUnPushMutation();
  const [deleteProducts] = useDeleteRetailderProductMutation();
  const totalCount = productData?.pagination?.total ?? 0;

  /* -------------------------------- functions ------------------------------- */
  const handleChangePage = (_event: unknown, newPage: number) => {
    setFilters({ page: newPage }, { history: "push" });
  };

  const handleChangeRowsPerPage = (event: SelectChangeEvent<number>) => {
    setFilters({ pageSize: event.target.value as number }, { history: "push" });
    setFilters({ page: 1 }, { history: "push" });
  };

  const cells = headerItems({ t });

  const handlePublish = (ids: string[]) => {
    hideModal();
    setTimeout(() => {
      showModal({
        body: (
          <div className="flex items-center justify-center w-full">
            <CircularProgress />
          </div>
        ),
        closable: false
      });

      pushProducts({ body: { ids: ids } })
        .then((res: any) => {
          if (!res?.error) {
            setCheckedIds([]);
          }
        })
        .catch((err: any) => clientDefaultErrorHandler({ error: err }))
        .finally(() => {
          hideModal();
        });
    }, 0);
  };

  const handleUnPublish = (ids: string[]) => {
    hideModal();
    setTimeout(() => {
      showModal({
        body: (
          <div className="flex items-center justify-center w-full">
            <CircularProgress />
          </div>
        ),
        closable: false
      });

      unpushProducts({ body: { ids: ids } })
        .then((res: any) => {
          if (!res?.error) {
            setCheckedIds([]);
          }
        })
        .catch((err: any) => clientDefaultErrorHandler({ error: err }))
        .finally(() => {
          hideModal();
        });
    }, 0);
  };

  const handleDelete = (ids: string[]) => {
    hideModal();
    setTimeout(() => {
      showModal({
        body: (
          <div className="flex items-center justify-center w-full">
            <CircularProgress />
          </div>
        ),
        closable: false
      });

      deleteProducts({ body: { ids: ids } })
        .then((res: any) => {
          if (!res?.error) {
            setCheckedIds([]);
          }
        })
        .catch((err: any) => clientDefaultErrorHandler({ error: err }))
        .finally(() => {
          hideModal();
        });
    }, 0);
  };

  const onClickPublish = (id: string[]) => {
    showModal({
      title: t("retailerProduct.publishModalTitle"),
      subTitle: t("retailerProduct.publishModalSubtitle"),
      icon: "/images/svgs/publish-01.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.yesPublish"),
          variant: "primary",
          onClick: () => handlePublish(id)
        }
      ]
    });
  };

  const onClickUnPublish = (id: string[]) => {
    showModal({
      title: t("retailerProduct.unpublishModalTitle"),
      subTitle: t("retailerProduct.unpublishModalSubtitle"),
      icon: "/images/svgs/unpublish-01.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.unpublish"),
          variant: "destructivePrimary",
          onClick: () => handleUnPublish(id)
        }
      ]
    });
  };

  const onClickDelete = (id: string[]) => {
    showModal({
      title: t("retailerProduct.unpublishModalTitle"),
      subTitle: t("retailerProduct.unpublishModalSubtitle"),
      icon: "/images/svgs/unpublish-01.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.yesUnpublish"),
          variant: "destructivePrimary",
          onClick: () => handleDelete(id)
        }
      ]
    });
  };

  const handleCheckAllItems = (checked: boolean) => {
    const ids = productData?.data
      // ?.filter(item => !checkedIds?.find(v => v === item?.id))
      ?.map(item => item.id) as string[];

    setCheckedIds(checked ? ids : []);
  };

  const handleCheckItem = (id: string) => {
    const existId = checkedIds?.find(item => item === id);

    if (existId) {
      setCheckedIds(prev => prev?.filter(item => item !== id));
    } else {
      setCheckedIds(prev => [...prev, id]);
    }
  };

  if (isMobile) {
    return (
      <ProductsTableMobile
        {...{
          productData,
          isDraft,
          onClickPublish,
          onClickUnPublish,
          onClickDelete,
          totalCount,
          pageSize: filters?.pageSize,
          page: filters?.page,
          handleChangePage,
          handleChangeRowsPerPage,
          isLoading: isProductLoading || isProductFetching,
          hasFilters: !finalFilters
        }}
      />
    );
  }

  if (isProductLoading) {
    return (
      <Box className="flex items-center justify-center h-full">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <CustomCardContent className="h-full flex flex-col">
      <>
        <Filters
          endAdornment={
            retailerStore?.integration?.platform?.key &&
            ["ShopBuilder"]?.includes(retailerStore?.integration?.platform?.key) && (
              <Link href={`${makePath(routes.retailerListCategories)}`}>
                <div className="flex items-center gap-1 ring-v2-content-on-info text-[13px] font-medium cursor-pointer">
                  <Icon icon="solar:widget-2-outline" width={20} height={20} /> {t("retailer.categoryPage.categories")}{" "}
                  <Icon icon="solar:alt-arrow-left-outline" width={16} height={16} />
                </div>
              </Link>
            )
          }
        />
        <Box mt={3} className="flex-1">
          {isProductFetching ? (
            <Box className="flex items-center justify-center h-full">
              <CircularProgress />
            </Box>
          ) : (!productData?.data?.length && !isProductFetching) || isProductError ? (
            <ProductEmptyList hasFilters={!finalFilters} isDraft={isDraft} />
          ) : (
            <Box className="flex flex-col h-full min-h-[60vh]">
              <TableContainer className="flex-1">
                <Table aria-label="product">
                  <TableHead className="sticky top-0 z-10">
                    <TableRow>
                      {checkedIds?.length ? (
                        <>
                          <TableCell className="py-1">
                            <Stack flexDirection="row" alignItems="center" gap={1}>
                              <CustomCheckbox
                                className="p-0"
                                indeterminate={!!checkedIds?.length && checkedIds?.length !== productData?.data?.length}
                                onChange={(_e, checked) => handleCheckAllItems(checked)}
                                checked={!!checkedIds?.length && checkedIds?.length === productData?.data?.length}
                              />

                              {isDraft && (
                                <div
                                  className="cursor-pointer rounded-lg border-[1.5px] border-v2-border-primary py-1 px-3.5 bg-v2-surface-primary flex items-center justify-center gap-2"
                                  onClick={() => onClickPublish(checkedIds)}
                                >
                                  <Icon icon="ph:plus-bold" color="rgb(var(--color-gray-400))" width={16} height={16} />
                                  <div className="text-v2-content-secondary text-[13px] font-medium">
                                    {t("retailerProduct.bulkPublish")}
                                  </div>
                                </div>
                              )}

                              {!isDraft && (
                                <div
                                  className="cursor-pointer rounded-lg border-[1.5px] border-v2-border-primary py-1 px-3.5 bg-v2-surface-primary flex items-center justify-center gap-2"
                                  onClick={() => onClickUnPublish(checkedIds)}
                                >
                                  <Icon icon="ph:plus-bold" color="rgb(var(--color-gray-400))" width={16} height={16} />
                                  <div className="text-v2-content-secondary text-[13px] font-medium">
                                    {t("retailerProduct.bulkUnPublish")}
                                  </div>
                                </div>
                              )}

                              <div
                                className="cursor-pointer rounded-lg border-[1.5px] border-v2-border-primary py-1 px-3.5 bg-v2-surface-primary flex items-center justify-center gap-2"
                                onClick={() => onClickDelete(checkedIds)}
                              >
                                <Icon
                                  icon="solar:trash-bin-trash-outline"
                                  color="rgb(var(--color-gray-400))"
                                  width={16}
                                  height={16}
                                />
                                <div className="text-v2-content-secondary text-[13px] font-medium">
                                  {t("retailerProduct.bulkDelete")}
                                </div>
                              </div>
                            </Stack>
                          </TableCell>
                          {[...Array.from({ length: cells?.length - 1 })].map((_, index) => (
                            <TableCell key={index} className="py-1" />
                          ))}
                        </>
                      ) : (
                        cells?.map(item => (
                          <TableCell key={item.id} width={item.size} className="py-1">
                            <Stack flexDirection="row" alignItems="center" gap={1}>
                              {item.hasCheckBox && (
                                <CustomCheckbox
                                  className="p-0"
                                  onChange={(_e, checked) => handleCheckAllItems(checked)}
                                  checked={!!checkedIds?.length && checkedIds?.length === productData?.data?.length}
                                />
                              )}
                              <div>{item.title}</div>
                            </Stack>
                          </TableCell>
                        ))
                      )}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {productData?.data?.map(item => (
                      <TableRow key={item.id}>
                        <TableCell width={cells[0].size}>
                          <Stack flexDirection="row" alignItems="center">
                            <CustomCheckbox
                              onChange={(_e, _checked) => handleCheckItem(item.id)}
                              checked={!!checkedIds?.length && checkedIds?.some(v => v === item?.id)}
                            />

                            <Avatar
                              src={item?.originProduct?.cover?.url}
                              alt={item?.originProduct?.cover?.alt || item.title}
                              className="rounded-md w-[46px] h-[46px]"
                            />
                            <div className="flex flex-col ms-5 gap-1">
                              <div className="text-[15px] font-medium text-v2-content-primary">{item.title}</div>
                              <div className="flex divide-x  divide-x-reverse  divide-gray-50 *:px-3">
                                <div className="!pr-0 text-v2-content-tertiary text-xs font-medium">
                                  {t("retailerProduct.supplier")}: {item?.originProduct?.supplier?.name}
                                </div>
                                <Link href={`${makePath(routes.product)}/${item.originProduct?.id}`} target="_blank">
                                  <div className="!pl-0 text-v2-content-on-info text-[13px] font-medium flex items-center gap-px">
                                    {t("retailerProduct.viewProduct")}{" "}
                                    <Icon icon="solar:arrow-left-up-linear" width={18} height={18} />
                                  </div>
                                </Link>
                              </div>
                            </div>
                          </Stack>
                        </TableCell>
                        <TableCell width={cells[1].size}>
                          <Stack flexDirection="row" alignItems="center" gap={1}>
                            <Link href={`${makePath(routes.editRetailerProduct)}/${item.id}`}>
                              <Button
                                variant="tertiaryGray"
                                startAdornment={<Icon icon="solar:pen-new-square-linear" />}
                                size="md"
                              >
                                {t("retailerProduct.edit")}
                              </Button>
                            </Link>
                            {isDraft && (
                              <Button
                                variant="secondaryGray"
                                onClick={() => onClickPublish([item.id])}
                                size="md"
                                endAdornment={<Icon icon="solar:alt-arrow-left-outline" />}
                              >
                                {t("retailerProduct.publish")}
                              </Button>
                            )}
                            {!isDraft && (
                              <Button
                                variant="destructiveSecondaryGray"
                                onClick={() => onClickUnPublish([item.id])}
                                size="md"
                                endAdornment={<Icon icon="solar:alt-arrow-left-outline" />}
                              >
                                {t("retailerProduct.unpublish")}
                              </Button>
                            )}
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Stack pt={3} mt={"auto"}>
                {productData?.data && (
                  <CustomTablePagination
                    rowsPerPageOptions={[10, 50, 100, 200]}
                    count={totalCount}
                    rowsPerPage={filters?.pageSize}
                    page={filters?.page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage={t("product.rowPerPage")}
                  />
                )}
              </Stack>
            </Box>
          )}
        </Box>
      </>
    </CustomCardContent>
  );
}

export default ProductsTable;
