import { inferParserType, parseAsInteger, parseAsString, useQueryStates } from "nuqs";

const productFiltersParsers = {
  title: parseAsString,
  page: parseAsInteger.withDefault(1),
  pageSize: parseAsInteger.withDefault(10),
  created_at: parseAsString.withDefault("asc"),
  updated_at: parseAsString
};

export const useFiltersState = () => {
  const [filters, setFilters] = useQueryStates(productFiltersParsers);

  return { filters, setFilters };
};

export type TUseFiltersState = inferParserType<typeof productFiltersParsers>;
