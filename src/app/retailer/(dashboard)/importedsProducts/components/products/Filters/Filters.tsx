import Input from "@/components/ui/inputs/Input";
import { Icon } from "@iconify/react";
import { Theme } from "@mui/material";
import { useMediaQuery } from "@mui/system";
import { debounce } from "lodash";
import React, { ReactNode, useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { useFiltersState } from "./useFiltersState";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import Button from "@/components/ui/Button";
import { TRetailerImportListData } from "@/store/apps/retailerProduct/types";

interface IFiltersProps {
  endAdornment?: ReactNode;
  handleCheckAllItems: (checked: boolean) => void;
  totalCount: number;
  checkedIds: string[];
  onClickPublish: (id: string[]) => void;
  onClickDelete: (id: string[]) => void;
  onClickUnPublish: (id: string[]) => void;
  isDraft: boolean;
  productData?: TRetailerImportListData;
}

function Filters({
  endAdornment,
  handleCheckAllItems,
  totalCount,
  checkedIds,
  onClickDelete,
  onClickPublish,
  onClickUnPublish,
  productData,
  isDraft
}: IFiltersProps) {
  const { t } = useTranslation();
  const { filters, setFilters } = useFiltersState();

  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const handleOnChange = useCallback(
    debounce((value: string) => {
      setFilters({ title: value, page: 1 }, { history: "push" });
    }, 2000),
    []
  );
  const [internalValue, setInternalValue] = useState<string | undefined>(filters?.title || undefined);

  const onReset = (key: string | string[]) => {
    if (Array.isArray(key) && key?.length) {
      key?.forEach(item => {
        setFilters({ [item]: null }, { history: "push" });
      });
    } else setFilters({ [key as any]: null }, { history: "push" });
  };

  return (
    <div className="bg-v2-surface-primary px-5 py-2 rounded-lg flex items-center gap-2 w-full h-14">
      <div className="flex items-center gap-2">
        <CustomCheckbox
          className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
          indeterminate={!!checkedIds?.length && checkedIds?.length !== productData?.data?.length}
          onChange={(_e, checked) => handleCheckAllItems(checked)}
          checked={!!checkedIds?.length && checkedIds?.length === productData?.data?.length}
        />
        {checkedIds?.length ? (
          <div className="text-v2-content-primary text-base font-medium">
            {checkedIds?.length} {t("selectedProduct")}
          </div>
        ) : (
          <div className="text-v2-content-primary text-base font-medium">
            {totalCount} {t("notSelectedProduct")}
          </div>
        )}
      </div>

      {checkedIds?.length > 0 ? (
        <div className="flex-1 flex items-center justify-end">
          {isDraft && (
            <>
              <Button
                variant="tertiaryGray"
                className="text-v2-content-on-error-2"
                onClick={() => onClickDelete(checkedIds)}
              >
                <Icon icon="solar:trash-bin-minimalistic-outline" width={20} height={20} />
                حذف محصولات
              </Button>
              <div className="w-px h-4 bg-v2-border-primary" />
              <Button
                variant="tertiaryGray"
                className="text-v2-content-on-info"
                onClick={() => onClickPublish(checkedIds)}
              >
                <Icon icon="ph:plus" width={20} height={20} />
                اضافە بە فروشگاه
              </Button>
            </>
          )}
          {!isDraft && (
            <Button
              variant="tertiaryGray"
              className="text-v2-content-on-error-2"
              onClick={() => onClickUnPublish(checkedIds)}
            >
              <Icon icon="solar:trash-bin-minimalistic-outline" width={20} height={20} />
              حذف از فروشگاه
            </Button>
          )}
        </div>
      ) : (
        <div className="flex-1 flex items-center gap-2 justify-end">
          <Input
            startAdornment={
              <Icon icon="solar:magnifer-linear" width="1.1rem" color="#ADADAD" height="1.1rem" className="ml-1.5" />
            }
            inputSize="sm"
            variant="filled"
            className="max-h-12"
            // inputParentClassName="bg-v2-surface-primary"
            rootClassName="md:w-50 shrink-0"
            value={internalValue || undefined}
            placeholder={`${t("chats.searchQuery")} ...`}
            onChange={e => {
              handleOnChange(e.target.value);
              setInternalValue(e.target.value);
            }}
          />
          <div className="flex items-center justify-center p-2.5 border border-v2-border-primary rounded-lg cursor-pointer">
            <Icon icon="solar:menu-dots-bold" width={20} height={20} className="rotate-90" />
          </div>
        </div>
      )}
      {endAdornment && <div className="flex justify-end items-end w-full">{endAdornment}</div>}
    </div>
  );
}

export default Filters;
