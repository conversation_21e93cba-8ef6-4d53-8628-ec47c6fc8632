import React, { useCallback } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TFormData } from "./types";
import InputLabel from "@/components/ui/inputs/Input/InputLabel";
import Editor from "@/components/ui/Editor/Editor";
import { debounce } from "lodash";

function Description({ manuallySubmitTheForm }: { manuallySubmitTheForm: VoidFunction }) {
  const { t } = useTranslation();
  const { control } = useFormContext<TFormData>();

  const handleAutoSave = useCallback(
    debounce(() => {
      manuallySubmitTheForm();
    }, 2000),
    []
  );

  return (
    <div>
      <Controller
        name="description"
        control={control}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <>
            <InputLabel htmlFor="description" containerClassName="mb-1">
              {t("product.description")}
            </InputLabel>
            <Editor
              initialValue={value}
              onChange={value => {
                onChange(value);
                handleAutoSave();
              }}
              placeholder={t("product.description")}
              error={Boolean(error?.message)}
              helperText={error?.message || t("product.descriptionHint")}
            />
          </>
        )}
      />
    </div>
  );
}

export default Description;
