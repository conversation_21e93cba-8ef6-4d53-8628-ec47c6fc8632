import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import { routes } from "@/constants/routes";
import { useGetRetailerImportsListQuery } from "@/store/apps/retailerProduct";
import { omitEmptyValues } from "@/utils/helpers";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { generateBackendFilters, generateBackendSorts } from "@/utils/services/transformers";
import { usePathname, useRouter } from "next/navigation";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import { useFiltersState } from "../products/Filters/useFiltersState";

function ProductsAppBar() {
  const pathname = usePathname();
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const router = useRouter();
  const lastSectionPathname = pathname.split("/")?.at(-1) ?? "";

  const { filters, setFilters } = useFiltersState();

  const { page, pageSize, created_at, updated_at, ...restFilters } = filters || {};

  const sorts = generateBackendSorts(
    omitEmptyValues({
      created_at: (created_at as any) || undefined,
      updated_at: (updated_at as any) || undefined
    })
  );

  const nonEmptyFilters = useMemo(() => omitEmptyValues(restFilters), [restFilters]);

  const isDraft = pathname.includes(routes.retailerProductsDrafts);

  const generalFinalFilters = generateBackendFilters({ ...nonEmptyFilters });
  const draftFinalFilters = generateBackendFilters({ ...nonEmptyFilters, draft: isDraft });

  const queryParts = [
    `page_size=${filters?.pageSize}`,
    `page=${filters?.page}`,
    generalFinalFilters ? `filters=${generalFinalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ].filter(part => part !== "");
  const queryString = queryParts.join("&");

  const draftQueryParts = [
    `page_size=${filters?.pageSize}`,
    `page=${filters?.page}`,
    draftFinalFilters ? `filters=${draftFinalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ].filter(part => part !== "");
  const draftQueryString = queryParts.join("&");

  /* ----------------------------------- rtk ---------------------------------- */
  const { data: productData, isLoading: isProductLoading } = useGetRetailerImportsListQuery(queryString || "", {
    skip: !filters?.page,
    refetchOnMountOrArgChange: true
  });

  const { data: draftProductData } = useGetRetailerImportsListQuery(draftQueryString || "", {
    skip: !filters?.page,
    refetchOnMountOrArgChange: true
  });

  const totalCount = productData?.pagination?.total ?? 0;
  const draftTotalCount = draftProductData?.pagination?.total ?? 0;
  const notDraftTotalCount = totalCount - draftTotalCount;

  const productStateItems = [
    {
      id: "drafts",
      label: t("product.drafts"),
      path: `${makePath(routes.retailerProductsDrafts)}`
    },
    {
      id: "imports",
      label: t("product.importeds"),
      path: `${makePath(routes.retailerProductsImports)}`
    }
  ];

  //   if (isProductLoading) {
  //     return (
  //       <div className="flex items-center justify-center h-[50vh] w-full">
  //         <CircularProgress />
  //       </div>
  //     );
  //   }

  return (
    <MobileAppBar
      className="!h-[96px] !justify-start pt-5"
      RenderComponent={
        <div className="relative">
          <div className="flex justify-between">
            <div className="flex items-center gap-1">
              <span className="text-body2-medium text-v2-content-primary">{t("products")}</span>
              <span className="text-body2-medium text-v2-content-tertiary">({totalCount})</span>
            </div>
          </div>
          <div className="absolute -bottom-14 left-0 w-full overflow-x-auto z-[999] ">
            <div className="flex items-center gap-1">
              {productStateItems?.map((item, index) => (
                <div
                  key={item?.id}
                  onClick={() => {
                    router.push(item?.path);
                  }}
                  className={twMerge(
                    "whitespace-nowrap border-b-transparent border-b-2 pb-2.5 flex items-center justify-center gap-1 w-full",
                    lastSectionPathname?.includes(item?.id) ? "text-purple-500 !border-b-v2-content-on-action-2" : ""
                  )}
                >
                  <span className="text-body3-medium text-v2-content-primary">{item?.label}</span>
                  <div className="bg-v2-surface-action-light text-body3-medium px-1.5 text-v2-content-on-action-hover-2 rounded-[4px] shrink-0 ">
                    {item.id === "drafts" ? draftTotalCount : notDraftTotalCount}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      }
    />
  );
}

export default ProductsAppBar;
