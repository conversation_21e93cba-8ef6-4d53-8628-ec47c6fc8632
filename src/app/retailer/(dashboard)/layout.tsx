"use client";

import Header from "@/components/containers/header/Header";
import RootLayout, { RootLayoutRef } from "@/components/layouts/RootLayout/RootLayout";
import { routes } from "@/constants/routes";
import { useGetRetailerProfileQuery, useGetRetailerStoresQuery } from "@/store/apps/retailer";
import { IntegrationDataItem } from "@/store/apps/retailer/types";
import {
  getRetailerStoreId,
  setIsError,
  setIsLoading,
  setRetailerStoreData,
  setRetailerStoreId
} from "@/store/apps/retailerStore/retailerStoreSlice";
import { useSelector } from "@/store/hooks";
import { AppState } from "@/store/store";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { CircularProgress } from "@mui/material";
import { Box, Theme, useMediaQuery } from "@mui/system";
import { usePathname, useRouter } from "next/navigation";
import React, { useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import StorePopover from "./store/StorePopover";
import Link from "next/link";

interface Props {
  children: React.ReactNode;
}

export default function Layout({ children }: Props) {
  const pathname = usePathname();
  const router = useRouter();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const layoutRef = useRef<RootLayoutRef>(null);
  const makeRoute = useRoleBasePath();
  const selectedRetailerStoreId = useSelector((state: AppState) => state?.retailerStore?.selectedRetailerStoreId);

  const {
    data: storeData,
    isError: isRetailerStoreError,
    isLoading: isRetailerStorLoading
  } = useGetRetailerStoresQuery();

  useEffect(() => {
    dispatch(setIsLoading(true));
    dispatch(setIsError(isRetailerStoreError));
    dispatch(getRetailerStoreId());
    setTimeout(() => {
      dispatch(setIsLoading(false));
    }, 100);

    setTimeout(() => {
      if (storeData) {
        let foundData: IntegrationDataItem | undefined;

        if (selectedRetailerStoreId) {
          foundData = storeData?.data?.find(item => item.id === selectedRetailerStoreId);
        }

        if (!foundData?.id && storeData?.data?.length > 0) {
          // Default to the first item in the array if no ID is found
          foundData = storeData.data[0];
          dispatch(setRetailerStoreId(foundData?.id ?? ""));
        }

        dispatch(setRetailerStoreData(foundData || null));
      }
    }, 0);
  }, [storeData, isRetailerStoreError, isRetailerStorLoading, selectedRetailerStoreId, dispatch]);

  const { error: getRetailerProfileError, isLoading: isLoadingRetailerProfile } = useGetRetailerProfileQuery();
  // const {
  //   error: getRetailerPlanError,
  //   isSuccess: isSuccessGetRetailerPlan,
  //   isLoading: isLoadingRetailerPlan
  // } = useGetRetailerPlanQuery();

  const isLoading = isLoadingRetailerProfile;
  const isInSubscriptionPage = pathname.includes(routes.subscription);
  const isInProfilePage = pathname.includes(routes.profile);
  const isInOnboardingPage = pathname.includes(routes.retailerOnBoarding);

  function CanViewDashboard() {
    // const notHavePlan = getRetailerPlanError?.status === 404;
    // const havePlan = isSuccessGetRetailerPlan && !notHavePlan;

    if (isLoading) return false;
    if (isInProfilePage || isInOnboardingPage) return true;
    // if (!havePlan && isInSubscriptionPage) return true;
    // if (havePlan) return true;

    return false;
  }

  const canViewDashboard = CanViewDashboard();

  const whiteList = ["/retailer", "/", ""];

  const isWhiteListRoute = whiteList?.some(item => item === pathname);

  function handleRedirect() {
    // if (isWhiteListRoute) return;

    // redirect to profile page if not have before
    if (getRetailerProfileError?.status === 428 && !isInProfilePage && !isWhiteListRoute) {
      router.replace(`${makeRoute(routes.profile)}?step=0&status=incomplete`);
    }

    if (isLoading) return;
    if (canViewDashboard) return;

    // redirect to onBoarding page if status is not `Active` (still `InReview` || `Rejected` || `Disabled`)
    // if (retailerProfileData?.data?.status !== "Active") {
    //   router.push(makeRoute(routes.home));
    //   return;
    // }

    // redirect to subscription page if not have any
    /**
     * todo: should be un commented after subscription was done
     */
    // if (getRetailerPlanError?.status === 404 && !isInSubscriptionPage) {
    //   router.push(makeRoute(routes.subscription));
    //   return;
    // }
  }

  useEffect(() => {
    router.prefetch(`${makeRoute(routes.profile)}?step=0&status=incomplete`);
    handleRedirect();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, getRetailerProfileError?.status, pathname, whiteList]);

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100dvh" }}>
        <CircularProgress />
      </Box>
    );
  }

  if (getRetailerProfileError?.status === 428 && !isInProfilePage && !isWhiteListRoute) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100dvh" }}>
        <CircularProgress />
      </Box>
    );
  }

  // if (canViewDashboard)

  return (
    <>
      <RootLayout ref={layoutRef}>
        {!isMobile && (
          <Header onToggleSidebar={() => layoutRef.current?.onToggleSidebar()} RenderStartSuffix={StorePopover} />
        )}
        {/* <StoreModal /> */}
        <Box >
          {children}
        </Box>
      </RootLayout>
    </>
  );
}
