/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import { FormikHelpers } from "formik";
import { FormElementData, InputType } from "@/store/apps/meta/types";
import { Box, CircularProgress, Grid } from "@mui/material";
import { routes } from "@/constants/routes";
import { useTranslation } from "react-i18next";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import useLanguage from "@/utils/hooks/useLanguage";
import useCurrency from "@/utils/hooks/useCurrency";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { clientDefaultErrorHandler, SetHookFormError } from "@/utils/services/utils";
import { Retailer, usePostRetailerStoreMutation, usePutRetailerStoreMutation } from "@/store/apps/retailer";
import { IntegrationsPutDataResponse, TRetailerStoreResponse } from "@/store/apps/retailer/types";
import { ensureUrlScheme, snakeToCamelCaseHookFormWrapper } from "@/utils/helpers";
import { useDispatch, useSelector } from "@/store/hooks";
import { RETAILER_STORE_KEY } from "@/constants/queryKeys";
import { AppState } from "@/store/store";
import DynamicForm from "@/components/containers/DynamicForm/DynamicForm";

interface IStoreDynamicFormProps {
  configForm: { [key: string]: FormElementData };
  isLoading: boolean;
  isEdit?: boolean;
  retailerStoreSingle?: TRetailerStoreResponse;
  isRetailerStoreSingleLoading?: boolean;
}
const StoreDynamicForm = ({
  isEdit,
  configForm,
  isLoading,
  retailerStoreSingle,
  isRetailerStoreSingleLoading
}: IStoreDynamicFormProps) => {
  const router = useRouter();
  const makePath = useRoleBasePath();
  const params = useParams();
  const searchParams = useSearchParams();
  const [currentLang] = useLanguage();
  const integrationId = searchParams.get("integrationId");
  const [_selected, _selectCurrency] = useCurrency();
  const id = params?.id || (integrationId as string);
  const dispatch = useDispatch();

  const { selectedRetailerStoreId } = useSelector((state: AppState) => ({
    selectedRetailerStoreId: state?.retailerStore?.selectedRetailerStoreId
  }));

  const [postRetailerStore, { isLoading: isPostRetailerStoreLoading }] = usePostRetailerStoreMutation();
  const [putRetailerStore, { isLoading: isPutRetailerStoreLoading }] = usePutRetailerStoreMutation();

  const isSubmitting = isPostRetailerStoreLoading || isPutRetailerStoreLoading;

  const initialValues = Object.keys(configForm).reduce(
    (acc, key) => {
      acc[key] = retailerStoreSingle?.data?.integration?.config?.[key] || "";
      return acc;
    },
    {} as { [key: string]: any }
  );

  const onSubmit = async (
    { preferences, ...values }: { [key: string]: any },
    { setFieldError }: FormikHelpers<typeof initialValues>
  ) => {
    const body = {
      config: values,
      preferences,
      platform: id,
      isActive: !!isEdit ? true : null,
      returnUri: `${makePath(routes.profile)}?settingType=store`
    } as any;

    try {
      const retailerStoreApi = isEdit
        ? putRetailerStore({ body, id: selectedRetailerStoreId ?? "" })
        : postRetailerStore({ body });

      await retailerStoreApi.then(res => {
        const error = (res as any)?.error?.data;

        const resData = (res as any)?.data?.data as IntegrationsPutDataResponse["data"];

        if (error) {
          if (error.error_detail.handle?.includes("already exists")) {
            const bodyError = {
              ...error,
              error_detail: { handle: ["userDuplicate"] }
            };

            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setFieldError
            });
          } else
            clientDefaultErrorHandler({
              bodyError: error,
              error: (res as any)?.error,
              setFieldError
            });
        } else if ("data" in res && res?.data) {
          if (resData?.shouldRedirect && resData?.redirectTo) {
            router.push(ensureUrlScheme(resData?.redirectTo));
          } else router.push(`${makePath(routes.profile)}?settingType=store`);

          const id = retailerStoreSingle?.data?.id;
          dispatch(Retailer.util.invalidateTags([{ type: RETAILER_STORE_KEY, id }]));
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  if (isLoading || isRetailerStoreSingleLoading) {
    return (
      <Box className="flex items-center justify-center">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      {retailerStoreSingle?.data?.integration?.platform?.name?.length && (
        <div className="flex items-center gap-4 py-2 px-3 bg-v2-surface-info rounded-lg mb-4">
          {retailerStoreSingle?.data?.integration?.platform?.logo && (
            <>
              <div
                className="[&>svg]:size-6"
                dangerouslySetInnerHTML={{ __html: retailerStoreSingle?.data?.integration?.platform?.logo }}
              />
            </>
          )}
          <div className="flex flex-col ">
            <p className="text-body3-medium text-v2-content-primary">
              {retailerStoreSingle?.data?.integration?.platform?.name?.find(l => l.iso === currentLang?.value)?.text}
            </p>
            {retailerStoreSingle?.data?.integration?.platform?.subtitle?.length && (
              <p className="text-caption-medium text-v2-content-tertiary">
                {
                  retailerStoreSingle?.data?.integration?.platform?.subtitle?.find(l => l.iso === currentLang?.value)
                    ?.text
                }
              </p>
            )}
          </div>
        </div>
      )}

      <DynamicForm
        isLoading={isLoading}
        configForm={configForm}
        handleSubmit={onSubmit}
        isSubmitting={isSubmitting}
        isStoreSingleLoading={isRetailerStoreSingleLoading}
        storeDataSingle={retailerStoreSingle?.data as any}
      />
    </>
  );
};

export default StoreDynamicForm;
