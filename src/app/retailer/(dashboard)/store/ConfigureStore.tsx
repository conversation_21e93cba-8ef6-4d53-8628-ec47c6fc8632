import { FormElementData } from "@/store/apps/meta/types";
import { IntegrationDataItem } from "@/store/apps/retailer/types";
import { useSelector } from "@/store/hooks";
import { AppState } from "@/store/store";
import { ensureUrlScheme } from "@/utils/helpers";
import useClipboard from "@/utils/hooks/useClipboard";
import useLanguage from "@/utils/hooks/useLanguage";
import { Icon } from "@iconify/react";
import TabContext from "@mui/lab/TabContext";
import { Avatar, Tab, Tabs, Theme, useMediaQuery } from "@mui/material";
import clsx from "clsx";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import StoreDynamicForm from "./StoreDynamicForm";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import Invoice from "./InvoiceBrand";
import { useGetRetailerStoreQuery } from "@/store/apps/retailer";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import StoreTab from "./StoreTab";

interface IConfigureStoreProps {
  configForm: { [key: string]: FormElementData };
  isLoading: boolean;
  isEdit?: boolean;
}

type ConfigureStoreType = "form" | "invoice";

function ConfigureStore({ configForm, isLoading, isEdit = false }: IConfigureStoreProps) {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const router = useRouter();
  const searchParams = useSearchParams();
  const type = searchParams?.get("type") as ConfigureStoreType;
  const storeId = searchParams.get("id");
  const [activeTab, setActiveTab] = useState(type);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const { selectedRetailerStoreId } = useSelector((state: AppState) => ({
    selectedRetailerStoreId: state?.retailerStore?.selectedRetailerStoreId
  }));

  const id = storeId || selectedRetailerStoreId || "";

  const { data: retailerStoreSingle, isLoading: isRetailerStoreSingleLoading } = useGetRetailerStoreQuery(
    {
      id: id
    },
    { skip: !id }
  );

  useEffect(() => {
    if (!type) {
      setActiveTab("form");
    }
  }, [type]);

  const retailerStoreSelector = useSelector(
    (state: any) => state?.Retailer?.queries["getRetailerStores(undefined)"] || {}
  );
  const retailerStoreData = retailerStoreSelector?.data as { data: IntegrationDataItem[] };
  // const isRetailerStoreLoading = retailerStoreSelector?.status === "pending";

  const selectedStore = retailerStoreData?.data?.find(item => item?.id === selectedRetailerStoreId);

  if (isMobile)
    return (
      <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6">
        <StoreTab activeTab={activeTab} setActiveTab={setActiveTab} retailerStoreSingle={retailerStoreSingle} />

        <div className="mt-4">
          {activeTab === "form" && (
            <StoreDynamicForm
              isEdit={isEdit}
              isLoading={isLoading}
              configForm={configForm}
              retailerStoreSingle={retailerStoreSingle}
              isRetailerStoreSingleLoading={isRetailerStoreSingleLoading}
            />
          )}
          {activeTab === "invoice" && (
            <Invoice
              retailerStoreSingle={retailerStoreSingle}
              isRetailerStoreSingleLoading={isRetailerStoreSingleLoading}
            />
          )}
        </div>
      </div>
    );

  return (
    <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6">
      <div
        className="flex gap-2 items-center text-v2-content-primary cursor-pointer mb-4"
        onClick={() => router.back()}
      >
        <Icon icon="fluent-mdl2:forward" className="text-v2-content-tertiary size-4" />
        <span className="text-body4-medium text-v2-content-secondary">{t("back")}</span>
      </div>

      <StoreTab activeTab={activeTab} setActiveTab={setActiveTab} retailerStoreSingle={retailerStoreSingle} />

      <div className="mt-4">
        {activeTab === "form" && (
          <StoreDynamicForm
            isEdit={isEdit}
            isLoading={isLoading}
            configForm={configForm}
            retailerStoreSingle={retailerStoreSingle}
            isRetailerStoreSingleLoading={isRetailerStoreSingleLoading}
          />
        )}
        {activeTab === "invoice" && (
          <Invoice
            retailerStoreSingle={retailerStoreSingle}
            isRetailerStoreSingleLoading={isRetailerStoreSingleLoading}
          />
        )}
      </div>
    </div>
  );
}

export default ConfigureStore;
