import React, { useState } from "react";
import { <PERSON>ton, IconButton, Theme, useMediaQuery } from "@mui/material";
import { Menu } from "@mui/material";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { useDispatch } from "react-redux";
import { usePathname, useRouter } from "next/navigation";
import { setRetailerStoreData, setRetailerStoreId } from "@/store/apps/retailerStore/retailerStoreSlice";
import { IntegrationDataItem, TRetailerProfileData } from "@/store/apps/retailer/types";
import useModal from "@/utils/hooks/useModal";
import { usePostRetailerStoreStateMutation, usePutRetailerStoreMutation } from "@/store/apps/retailer";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import StoreModalBody from "../StoreWarningModalBody";

interface IStoreMoreMenuProps {
  item: IntegrationDataItem;
  config?: {
    [key: string]: any;
  };
  integrationId?: string;
  selectedRetailerStoreId?: string | null;
  activeProfile: boolean;
  handleNotActiveUser: () => void;
}

const StoreMoreMenu = ({
  item,
  config,
  integrationId,
  selectedRetailerStoreId,
  activeProfile,
  handleNotActiveUser
}: IStoreMoreMenuProps) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const pathname = usePathname();
  const router = useRouter();
  const { showModal, hideModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const [anchorEl, setAnchorEl] = useState(null);

  const [putRetailerStoreState, { isLoading: isPutRetailerStoreLoading }] = usePostRetailerStoreStateMutation();

  const handleClick2 = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleActiveAndDeActive = async (isActive: boolean) => {
    if (!activeProfile) {
      handleNotActiveUser();
      return;
    } else {
      const body = {
        retailerId: item?.retailerId,
        isActive
      };

      try {
        const retailerStoreApi = putRetailerStoreState({
          body: body,
          sid: selectedRetailerStoreId ?? ""
        });

        await retailerStoreApi.then((res: any) => {
          if ("data" in res && res?.data && !isActive) {
            showModal({
              body: (
                <StoreModalBody
                  title={t("store.disconnected.title")}
                  subtitle={t("store.disconnected.subtitle")}
                  buttonText={t("confirm")}
                />
              ),
              width: isMobile ? undefined : 428
            });
          }

          if (res?.error) {
            clientDefaultErrorHandler({ error: res.error });
            return;
          }
        });
      } catch (err: any) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  };

  const onConfirmDisconnect = (isActive: boolean) => {
    showModal({
      icon: "/images/svgs/redWarning.svg",
      title: t("disconnectTitle"),
      subTitle: t("disconnectSubtitle"),
      actions: [
        {
          label: t("cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("disconnectConnection"),
          variant: "accentPrimary",
          onClick: () => handleActiveAndDeActive(isActive)
        }
      ]
    });
  };

  const onConfirmConnect = (isActive: boolean) => {
    showModal({
      icon: "/images/svgs/successConnection.svg",
      title: t("connectTitle"),
      subTitle: t("connectSubtitle"),
      actions: [
        {
          label: t("cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("connect"),
          className: "!bg-success-500",
          variant: "accentPrimary",
          onClick: () => handleActiveAndDeActive(isActive)
        }
      ]
    });
  };

  return (
    <>
      <IconButton id="store-more-menu" onClick={handleClick2}>
        <Icon icon="qlementine-icons:menu-dots-16" className="size-[18px]" />
      </IconButton>
      <div className="rounded-lg shadow-[0px 2px 8px 0px rgba(0, 0, 0, 0.06)]" />
      <Menu
        id="store-more-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        classes={{
          paper: "rounded-lg border border-v2-border-secondary shadow-[0px 2px 8px 0px rgba(0, 0, 0, 0.06)]"
        }}
      >
        <div className="flex flex-col">
          <div
            className="pr-3 pl-5 py-2 flex items-center cursor-pointer gap-2 border-b border-b-v2-border-primary"
            onClick={() => {
              if (!activeProfile) {
                handleNotActiveUser();
                return;
              }
              dispatch(setRetailerStoreId(item?.id));
              dispatch(setRetailerStoreData(item));
              window.location.reload();
            }}
          >
            <Icon icon="teenyicons:tick-circle-outline" className="size-4" />
            <span className="text-v2-content-primary text-body4-medium">{t("selectStore")}</span>
          </div>
          <div
            className="pr-3 pl-5 py-2 flex items-center gap-2 border-b border-b-v2-border-primary cursor-pointer"
            onClick={() => {
              if (!activeProfile) {
                handleNotActiveUser();
                return;
              }
              router.push(
                `${pathname}?settingType=configureStore&integrationId=${integrationId}&id=${item?.id}&type=form&edit={true}`
              );
            }}
          >
            <Icon icon="basil:settings-adjust-outline" className="size-4" />
            <span className="text-v2-content-primary text-body4-medium">{t("setting")}</span>
          </div>
          {item?.isActive ? (
            <div
              className="pr-3 pl-5 py-2 flex items-center gap-2 cursor-pointer"
              onClick={() => onConfirmDisconnect(false)}
            >
              <Icon icon="solar:folder-path-connect-outline" className="size-4" />
              <span className="text-v2-content-primary text-body4-medium">{t("deActivation")}</span>
            </div>
          ) : (
            <div
              className="pr-3 pl-5 py-2 flex items-center gap-2 cursor-pointer"
              onClick={() => onConfirmConnect(true)}
            >
              <Icon icon="solar:folder-path-connect-outline" className="size-4" />
              <span className="text-v2-content-primary text-body4-medium">{t("activation")}</span>
            </div>
          )}
        </div>
      </Menu>
    </>
  );
};

export default StoreMoreMenu;
