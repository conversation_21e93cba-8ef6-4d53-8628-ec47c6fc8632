import React from "react";
import { Button, CircularProgress, Theme, useMediaQuery } from "@mui/material";
import { usePostRetailerStoreStateMutation, usePutRetailerStoreMutation } from "@/store/apps/retailer";
import useModal from "@/utils/hooks/useModal";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import StoreModalBody from "./StoreWarningModalBody";

type RetailerStoreProps = {
  config?: {
    [key: string]: any;
  };
  integrationId?: string;
  retailerId: string;
  selectedRetailerStoreId?: string | null;
  disabled?: boolean;
  handleNotActiveUser?: () => void;
  onclose?: () => void;
  defaultIsActive: boolean;
};

const DisconnectStoreButton: React.FC<RetailerStoreProps> = ({
  config,
  integrationId,
  retailerId,
  defaultIsActive,
  selectedRetailerStoreId,
  disabled,
  onclose,
  handleNotActiveUser
}) => {
  const { t } = useTranslation();
  const { showModal, hideModal } = useModal();
  const [putRetailerStoreState, { isLoading: isPutRetailerStoreLoading }] = usePostRetailerStoreStateMutation();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const handleActiveAndDeActive = async (isActive: boolean) => {
    onclose?.();
    if (handleNotActiveUser && disabled) {
      return handleNotActiveUser();
    } else {
      const body = {
        retailerId,
        isActive
      };

      try {
        const retailerStoreApi = putRetailerStoreState({ body, sid: selectedRetailerStoreId ?? "" });

        await retailerStoreApi.then((res: any) => {
          if ("data" in res && res?.data && !isActive) {
            showModal({
              body: (
                <StoreModalBody
                  title={t("store.disconnected.title")}
                  subtitle={t("store.disconnected.subtitle")}
                  buttonText={t("confirm")}
                />
              ),
              width: isMobile ? undefined : 428
            });
          }

          if (res?.error) {
            clientDefaultErrorHandler({ error: res.error });
            return;
          }
        });
      } catch (err: any) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  };

  const onConfirmDisconnect = (isActive: boolean) => {
    showModal({
      icon: "/images/svgs/redWarning.svg",
      title: t("disconnectTitle"),
      subTitle: t("disconnectSubtitle"),
      actions: [
        {
          label: t("cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("disconnectConnection"),
          variant: "accentPrimary",
          onClick: () => handleActiveAndDeActive(isActive)
        }
      ]
    });
  };

  const onConfirmConnect = (isActive: boolean) => {
    showModal({
      icon: "/images/svgs/successConnection.svg",
      title: t("connectTitle"),
      subTitle: t("connectSubtitle"),
      actions: [
        {
          label: t("cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("connect"),
          className: "!bg-success-500",
          variant: "accentPrimary",
          onClick: () => handleActiveAndDeActive(isActive)
        }
      ]
    });
  };

  if (defaultIsActive) {
    return (
      <Button
        variant="contained"
        color="info"
        onClick={() => onConfirmDisconnect(false)}
        className="bg-gray-20 hover:bg-gray-50 !shadow-none text-gray-600 text-body4-medium rounded-md py-3 max-h-10 px-4"
        startIcon={
          !isPutRetailerStoreLoading && (
            <Icon icon="solar:folder-path-connect-linear" className="text-purple-gray-600" />
          )
        }
      >
        {isPutRetailerStoreLoading ? (
          <CircularProgress size={21} />
        ) : (
          <div className="flex items-center justify-between w-full">
            <span>{t("store.disconnect")}</span>
            <Icon icon="solar:alt-arrow-left-linear" className="text-gray-600" />
          </div>
        )}
      </Button>
    );
  }
  return (
    <Button
      variant="contained"
      color="info"
      onClick={() => onConfirmConnect(true)}
      className="bg-gray-20 hover:bg-gray-50 !shadow-none text-gray-600 text-body4-medium rounded-md py-3 max-h-10 px-4"
      startIcon={
        !isPutRetailerStoreLoading && <Icon icon="solar:folder-path-connect-linear" className="text-purple-gray-600" />
      }
    >
      {isPutRetailerStoreLoading ? (
        <CircularProgress size={21} />
      ) : (
        <div className="flex items-center justify-between w-full">
          <span>{t("store.connect")}</span>
          <Icon icon="solar:alt-arrow-left-linear" className="text-gray-600" />
        </div>
      )}
    </Button>
  );
};

export default DisconnectStoreButton;
