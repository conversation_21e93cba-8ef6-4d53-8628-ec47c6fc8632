import "./AccountInfo.css";
import { useGetSupplierProfileQuery } from "@/store/apps/supplier";
import { Box, CircularProgress, Grid, Stack, Typography } from "@mui/material";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { routes } from "@/constants/routes";
import { Icon } from "@iconify/react";
import clsx from "clsx";
import Link from "next/link";
import { accountTypeValue } from "./utils";
import AccountLanguage from "./AccountLanguage";
import AccountCurrency from "./AccountCurrency";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import useModal from "@/utils/hooks/useModal";
import AuthChangePassword from "@/app/auth/authForms/AuthChangePassword";
import { USER_TYPES } from "@/constants/userTypes";
import PasswordChangeSuccess from "@/app/auth/authForms/PasswordChangeSuccess";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { ensureUrlScheme } from "@/utils/helpers";
import { useSelector } from "@/store/hooks";
import { shallowEqual } from "react-redux";
import { TMeResponse } from "@/store/apps/auth/types";

const AccountInfo = () => {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { data: profileData, isLoading, isError } = useGetSupplierProfileQuery();
  const { showModal } = useModal();

  const me = useSelector(state => state?.Auth?.queries[`getMe(undefined)`]?.data, shallowEqual) as TMeResponse;
  const username = me?.data?.phoneNumber || me?.data?.email;

  const fixedWebsite = ensureUrlScheme(profileData?.data?.website ?? "");

  const handleSuccess = () => {
    showModal({
      icon: "/images/svgs/password-successfull.svg",
      body: <PasswordChangeSuccess isLogin={false} redirectPath={makePath(routes.home)} />
    });
  };

  const handleOpenModal = () => {
    showModal({
      body: (
        <AuthChangePassword
          username={username}
          onSuccess={handleSuccess}
          userType={USER_TYPES.SUPPLIER}
          className="supplier-profile-change-password"
          submitText={t("supplier.profile.saveChanges")}
        />
      ),
      icon: "/images/svgs/change-password.svg"
    });
  };

  return (
    <Box className="account-info-wrapper">
      <Box className="account-info-container">
        <Stack direction="row" gap={1} flexWrap="wrap" className="account-info-stack-wrapper">
          <Box className={clsx("account-info-overallInfo-wrapper", "account-info-overallInfo-wrapper-col2")}>
            {isError ? (
              <Box className="account-info-overallInfo-loading-wrapper">
                <Typography>{t("errors.somethingWentWrong")}</Typography>
              </Box>
            ) : isLoading ? (
              <Box className="account-info-overallInfo-loading-wrapper">
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2} mb={2}>
                  {!!profileData?.data?.logo && (
                    <Image
                      alt="profile"
                      src={profileData?.data?.logo}
                      width={48}
                      height={48}
                      className="account-info-overallInfo-logo"
                    />
                  )}

                  <Box>
                    <Typography className="account-info-overallInfo-text-title">
                      {t("supplier.profile.storeName")}
                    </Typography>
                    <Typography className="account-info-overallInfo-text-value">{profileData?.data?.name}</Typography>
                  </Box>
                  <Link
                    href={profileData?.data?.id ? `${makePath(routes.supplierProducts(profileData?.data?.id))}` : ""}
                  >
                    <CustomButton
                      color="secondary"
                      startIcon={<Icon icon="solar:eye-outline" width={18} height={18} color="#404040" />}
                    >
                      {t("supplier.profile.previewShop")}
                    </CustomButton>
                  </Link>
                </Stack>

                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Typography className="account-info-overallInfo-text-title">
                      {t("supplier.profile.sectionInfo.accountType")}
                    </Typography>
                    <Typography className="account-info-overallInfo-text-value">
                      {profileData?.data?.identity.isLegalPerson
                        ? t("supplier.profile.registerAs.legal")
                        : t("supplier.profile.registerAs.personal")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography className="account-info-overallInfo-text-title">
                      {t("supplier.profile.phone")}
                    </Typography>
                    <Typography className="account-info-overallInfo-text-value">
                      {profileData?.data?.contactNumber ? profileData?.data?.contactNumber : "-"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography className="account-info-overallInfo-text-title">
                      {t("supplier.profile.email")}
                    </Typography>
                    <Typography className="account-info-overallInfo-text-value">
                      {profileData?.data?.contactEmail}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={4} mt={1}>
                    <Typography className="account-info-overallInfo-text-title">
                      {t("supplier.profile.website")}
                    </Typography>
                    <Link href={fixedWebsite} target="_blank" rel="noopener noreferrer">
                      <Stack flexDirection="row" gap={0.25} alignItems="center">
                        <Typography className="account-info-overallInfo-text-value">
                          {t("supplier.profile.linkWebsite")}
                        </Typography>
                        <Icon
                          icon="mynaui:external-link"
                          width={14}
                          height={14}
                          color="#404040"
                          rotate={"270deg" as any}
                        />
                      </Stack>
                    </Link>
                  </Grid>

                  {profileData?.data?.status && (
                    <Grid item xs={12} md={4} mt={1}>
                      <Typography className="account-info-overallInfo-text-title">
                        {t("supplier.profile.accountstatus")}
                      </Typography>
                      <Typography className="account-info-overallInfo-text-value">
                        {accountTypeValue({ t })[profileData?.data?.status]?.title}
                      </Typography>
                    </Grid>
                  )}
                </Grid>

                <Box className="account-info-overallInfo-button-wrapper">
                  <CustomButton
                    LinkComponent={Link}
                    href={`${makePath(routes.profile)}?step=0`}
                    endIcon={
                      <Icon
                        icon="lucide:arrow-up"
                        width={20}
                        height={20}
                        className="account-info-overallInfo-button-icon"
                      />
                    }
                  >
                    {t("supplier.profile.overallInfo")}
                  </CustomButton>
                </Box>
              </>
            )}
          </Box>

          <Box className={clsx("account-info-overallInfo-wrapper", "account-info-overallInfo-wrapper-col1")}>
            <Box className="account-info-overallInfo-icon-wrapper">
              <Icon icon="solar:password-outline" color="rgb(var(--color-gray-999))" width={24} height={24} />
            </Box>
            <Stack mt={6} className="account-info-change-password">
              <Typography className="account-info-overallInfo-change-password-text">
                {t("supplier.profile.changePassword.title")}
              </Typography>
              <Typography mt={0.5} className="account-info-overallInfo-change-password-subtitle">
                {t("supplier.profile.changePassword.subtitle")}
              </Typography>
            </Stack>

            <CustomButton
              onClick={handleOpenModal}
              className="account-info-overallInfo-change-password-button"
              color="secondary"
            >
              {t("supplier.profile.changePassword.buttonText")}
            </CustomButton>
          </Box>
        </Stack>

        <Stack direction="row" rowGap={2} columnGap={1} flexWrap="wrap">
          <Box className={clsx("account-info-overallInfo-wrapper", "account-info-overallInfo-wrapper-col")}>
            <AccountCurrency />
          </Box>
          <Box className={clsx("account-info-overallInfo-wrapper", "account-info-overallInfo-wrapper-col")}>
            <AccountLanguage />
          </Box>
        </Stack>
      </Box>
    </Box>
  );
};

export default AccountInfo;
