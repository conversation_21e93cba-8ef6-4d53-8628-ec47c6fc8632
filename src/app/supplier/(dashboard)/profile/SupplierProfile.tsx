import "./SupplierProfile.css";

import { USER_TYPES } from "@/constants/userTypes";
import ProfileStepper from "@/components/containers/ProfileStepper/ProfileTopStepper";
import SupplierInfo, { IsupplierDataRefProps } from "./SupplierInfo/SupplierInfo";
import SupplierStore, { ISupplierStore } from "./SupplierStore/SupplierStore";
import SupplierShipping from "./SupplierShipping/SupplierShipping";
import checkValues from "lodash/values";
import {
  useGetSupplierProfileQuery,
  useGetSupplierShippingQuery,
  usePostSupplierDocumentsMutation,
  usePostSupplierProfileMutation,
  usePutSupplierProfileMutation
} from "@/store/apps/supplier";
import { Box, Theme, useMediaQuery } from "@mui/material";
import { CircularProgress } from "@mui/material";
import { useCallback, useEffect, useRef, useState } from "react";
import SupplierAddress, { TSupplierAddress } from "./SupplierAddress/SupplierAddress";
import SupplierIReturn from "./SupplierReturn/SupplierIReturn";
import { TSupplierDocumentsPostBodyData, TSupplierProfileData } from "@/store/apps/supplier/types";
import { useTranslation } from "react-i18next";
import { clientDefaultErrorHandler, SetHookFormError } from "@/utils/services/utils";
import { isValidUUID, snakeToCamelCaseHookFormWrapper, SnakeToCamelFieldErrorWrapper } from "@/utils/helpers";
import useCurrency from "@/utils/hooks/useCurrency";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { handleGoToHasErrorTab } from "./utils";
import useModal from "@/utils/hooks/useModal";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { isEmpty } from "lodash";
import { TDocuments } from "./SupplierInfo/types";
import SupplierSignContractModal from "@/components/containers/ProfilesSignContractModal/SupplierSignContractModal";
import { UseFormSetError } from "react-hook-form";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import ProfileContentContainer from "@/components/containers/ProfileStepper/ProfileContentContainer";
import SupplierBank from "./SupplierBank/SupplierBank";
import { dateConverter } from "@/utils/helpers/dateHelpers";

export type TSupplierProfile = {
  identity?: TSupplierProfileData["identity"];
  id?: string;
  documents?: TDocuments;
} & ISupplierStore &
  TSupplierAddress;

function SupplierProfile() {
  const { t } = useTranslation();
  const profileInfoRef = useRef<IsupplierDataRefProps>(null);
  const profileStoreRef = useRef<IsupplierDataRefProps>(null);
  const profileAddressRef = useRef<IsupplierDataRefProps>(null);
  const [currency, selectCurrency, curencyOptions, currencyId] = useCurrency();
  const [supplierProfile, setSupplierProfile] = useState<TSupplierProfile>();
  const searchParams = useSearchParams();
  const inComplete = searchParams?.get("status") === "incomplete";
  const router = useRouter();
  const pathname = usePathname();
  const { showModal, hideModal } = useModal();
  const makeRoute = useRoleBasePath();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const documents: { tag: string; documents: TSupplierDocumentsPostBodyData[] }[] = !isEmpty(supplierProfile?.documents)
    ? Object.entries(supplierProfile?.documents!)?.map(([k, v]) => ({
        tag: k,
        documents: v
          ?.map(item => ({
            tag: k,
            mediaId: item?.id || ""
          }))
          ?.filter(a => a.mediaId)
      }))
    : [];

  const activeStepParams = searchParams?.get("step");
  const [activeStep, setActiveStep] = useState<undefined | number>(
    isMobile ? undefined : activeStepParams ? +activeStepParams : 0
  );

  const {
    data: supplierData,
    isLoading: isGetSupplierLoading,
    isSuccess: supplierDataSucceeded,
    isError: supplierDataFailed
  } = useGetSupplierProfileQuery();
  const { data: supplierShipping } = useGetSupplierShippingQuery({});

  const hasSupplier = !!checkValues(supplierData?.data).length;
  const hasSupplierShipping = !!supplierShipping?.data?.length;
  const termsAndConditionsApproved = !!supplierData?.data?.termsAndConditionsApproved || false;
  const [isUploadingDoc, setIsUploadingDoc] = useState(false);

  const [putSupplierProfile, { isLoading: isPostProfileLoading }] = usePutSupplierProfileMutation();
  const [postSupplierProfile, { isLoading: isPutProfileLoading }] = usePostSupplierProfileMutation();

  /* ------------------------------- upload doc ------------------------------- */
  const [postSupplierDoc, { isLoading: isLoadingDocuments }] = usePostSupplierDocumentsMutation();

  const onPostSupplierDoc = async (documents: { tag: string; documents: TSupplierDocumentsPostBodyData[] }[]) => {
    const isLegal = supplierProfile?.identity?.isLegalPerson;
    const documentValues: TSupplierDocumentsPostBodyData[] = documents
      ?.filter(item => (isLegal ? item?.tag?.startsWith("legal") : !item?.tag?.startsWith("legal")))
      .flatMap(item =>
        item.documents.map(doc => ({
          tag: item.tag,
          mediaId: doc.mediaId
        }))
      );

    setIsUploadingDoc(true);

    const body = documentValues;

    try {
      await postSupplierDoc({
        body
      }).then(res => {
        if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
          setIsUploadingDoc(false);
        }

        if ("data" in res && res?.data) {
          setActiveStep(4);
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const isProfileLoading = isPostProfileLoading || isPutProfileLoading;

  useEffect(() => {
    if (!inComplete) return;

    showModal({
      icon: "/images/svgs/danger.svg",
      // title: t("errorTitle"),
      subTitle: t("supplierProductWarning"),
      actions: [
        {
          label: t("confirm"),
          onClick: () => {
            router.replace(`${makeRoute(routes.profile)}?step=0`);
            hideModal();
          }
        }
      ],
      onClose: () => router.replace(`${makeRoute(routes.profile)}?step=0`)
    });
  }, [inComplete]);

  useEffect(() => {
    if (isUploadingDoc) return;

    if (activeStepParams === "3" && !hasSupplier) return;
    if (activeStepParams === "4" && (!hasSupplier || !hasSupplierShipping)) return;

    if (activeStepParams) setActiveStep(+activeStepParams);
  }, [activeStepParams, hasSupplier, hasSupplierShipping, isUploadingDoc]);

  useEffect(() => {
    const supplierAddress: TSupplierAddress = {
      address: {
        address1: supplierData?.data?.address?.address1,
        locationId:
          supplierData?.data?.address?.locationId && isValidUUID(supplierData?.data?.address?.locationId)
            ? supplierData?.data?.address?.locationId
            : undefined,
        zip: supplierData?.data?.address?.zip
      },
      warehouseAddress: {
        address1: supplierData?.data?.warehouseAddress?.address1,
        locationId:
          supplierData?.data?.warehouseAddress?.locationId &&
          isValidUUID(supplierData?.data?.warehouseAddress?.locationId)
            ? supplierData?.data?.warehouseAddress?.locationId
            : undefined,
        zip: supplierData?.data?.warehouseAddress?.zip,
        phoneNumber: supplierData?.data?.warehouseAddress?.phoneNumber
      }
    };
    const supplierStore: ISupplierStore = {
      bankAccount: supplierData?.data?.bankAccount,
      contactEmail: supplierData?.data?.contactEmail,
      contactNumber: supplierData?.data?.contactNumber,
      biography: supplierData?.data?.biography,
      logo: supplierData?.data?.logo,
      cover: supplierData?.data?.cover,
      name: supplierData?.data?.name,
      processingTime: supplierData?.data?.processingTime,
      website: supplierData?.data?.website
    };

    if (!!supplierData?.data)
      setSupplierProfile({
        identity: supplierData?.data?.identity,
        ...supplierStore,
        ...supplierAddress
      });
  }, [supplierData]);

  const handleShowSuccessModal = () => {
    showModal({
      icon: "/images/svgs/paySuccess.svg",
      body: (
        <p className="mt-4 xmd:mb-0 mb-4 text-center w-full xmd:text-subtitle-bold text-subtitle-bold text-gray-999">
          {hasSupplier ? t("updateProfile") : t("successfullProfile")}
        </p>
      ),
      actions: [
        {
          label: t("confirm"),
          onClick: () => {
            router.replace(makeRoute(routes.home));
            hideModal();
          }
        }
      ],
      modalProps: {
        containerClassName: "pt-7"
      }
    });
  };

  const handleShowSignContractModal = () => {
    showModal({
      width: 801,
      modalProps: { showCloseIcon: false },
      body: (
        <SupplierSignContractModal
          address={supplierProfile?.address?.address1}
          birthCertificateNumber={supplierProfile?.identity?.birthDay}
          email={supplierProfile?.contactEmail}
          fullName={supplierProfile?.name}
          nationalId={supplierProfile?.identity?.nationalCode}
          phoneNumber={supplierProfile?.contactNumber}
          postalCode={supplierProfile?.address?.zip}
          close={hideModal}
          onSuccess={() => {
            hideModal();
            handleShowSuccessModal();
          }}
        />
      )
    });
  };

  const onProfileSaved = () => {
    if (!termsAndConditionsApproved) {
      handleShowSignContractModal();
    } else {
      handleShowSuccessModal();
    }
  };

  const onSubmitProfile = async (data: TSupplierProfile, setFieldError?: SetHookFormError) => {
    const { documents: dataDocuments, ...restData } = data;
    const body = { ...restData, currencyId } as TSupplierProfileData;

    try {
      const supplierProfileApi = hasSupplier ? putSupplierProfile({ body }) : postSupplierProfile({ body });

      await supplierProfileApi.then(res => {
        const error = (res as any)?.error?.data;

        if (error) {
          handleGoToHasErrorTab(error, setActiveStep);

          setTimeout(() => {
            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setHookFormFieldError: snakeToCamelCaseHookFormWrapper(profileInfoRef.current?.handleError)
            });
            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setHookFormFieldError: snakeToCamelCaseHookFormWrapper(profileStoreRef.current?.handleError)
            });
            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setHookFormFieldError: snakeToCamelCaseHookFormWrapper(profileAddressRef.current?.handleError)
            });
            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setHookFormFieldError: snakeToCamelCaseHookFormWrapper(setFieldError)
            });
          }, 0);
        }
        if ("data" in res && res?.data) {
          if (hasSupplier) setActiveStep(4);
          if (!hasSupplier) {
            onPostSupplierDoc(documents);
          }
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const RenderContent = useCallback(() => {
    switch (activeStep) {
      case 0: {
        return (
          <>
            <MobileAppBar title={t("accountInfo")} hasBack onBack={() => setActiveStep(undefined)} />
            <ProfileContentContainer>
              {supplierDataSucceeded || supplierDataFailed ? (
                <SupplierInfo
                  isEdit={hasSupplier}
                  ref={profileInfoRef}
                  activeStep={activeStep}
                  supplierData={supplierProfile}
                  onChangeActiveStep={setActiveStep}
                  onSubmitData={({ documents, ...restData }) =>
                    setSupplierProfile(prev => ({
                      ...prev,
                      identity: restData,
                      documents
                    }))
                  }
                />
              ) : (
                isGetSupplierLoading && (
                  <Box id="sx-supplierprofile-16768">
                    <CircularProgress />
                  </Box>
                )
              )}
            </ProfileContentContainer>
          </>
        );
      }
      case 1:
        return (
          <>
            <MobileAppBar title={t("storeInfo")} hasBack onBack={() => setActiveStep(0)} />
            <ProfileContentContainer>
              {supplierDataSucceeded || supplierDataFailed ? (
                <SupplierStore
                  ref={profileStoreRef}
                  activeStep={activeStep}
                  onChangeActiveStep={setActiveStep}
                  supplierData={supplierProfile}
                  onPrev={data => setSupplierProfile(prev => ({ ...prev, ...data }))}
                  onSubmitData={data => setSupplierProfile(prev => ({ ...prev, ...data }))}
                />
              ) : (
                isGetSupplierLoading && (
                  <Box id="sx-supplierprofile-16768">
                    <CircularProgress />
                  </Box>
                )
              )}
            </ProfileContentContainer>
          </>
        );
      case 2: {
        return (
          <>
            <MobileAppBar title={t("addresses")} hasBack onBack={() => setActiveStep(1)} />
            <ProfileContentContainer>
              {supplierDataSucceeded || supplierDataFailed ? (
                <SupplierAddress
                  ref={profileAddressRef}
                  isLoading={isProfileLoading}
                  activeStep={activeStep}
                  onChangeActiveStep={setActiveStep}
                  supplierData={supplierProfile}
                  onPrev={data => setSupplierProfile(prev => ({ ...prev, ...data }))}
                  onSubmitData={data => setSupplierProfile(prev => ({ ...prev, ...data }))}
                />
              ) : (
                isGetSupplierLoading && (
                  <Box id="sx-supplierprofile-16768">
                    <CircularProgress />
                  </Box>
                )
              )}
            </ProfileContentContainer>
          </>
        );
      }
      case 3: {
        return (
          <>
            <MobileAppBar title={t("bankInfo")} hasBack onBack={() => setActiveStep(2)} />
            <ProfileContentContainer>
              {/* {supplierDataSucceeded || supplierDataFailed ? ( */}
              <SupplierBank
                ref={profileStoreRef}
                activeStep={activeStep}
                onChangeActiveStep={setActiveStep}
                supplierData={supplierProfile}
                isLoading={isProfileLoading || isUploadingDoc}
                onPrev={data => setSupplierProfile(prev => ({ ...prev, ...data }))}
                onSubmitData={(data, setFieldError) => {
                  setSupplierProfile(prev => ({ ...prev, ...data }));
                  onSubmitProfile({ ...supplierProfile, ...data }, setFieldError);
                }}
              />
              {/* ) : (
                (isGetSupplierLoading || isUploadingDoc) && (
                  <Box id="sx-supplierprofile-16768">
                    <CircularProgress />
                  </Box>
                )
              )} */}
            </ProfileContentContainer>
          </>
        );
      }
      case 4: {
        return (
          <>
            {/* <MobileAppBar title={t("shipping")} hasBack onBack={() => setActiveStep(undefined)} />
            <ProfileContentContainer> */}
            <SupplierShipping activeStep={activeStep} onChangeActiveStep={setActiveStep} />
            {/* </ProfileContentContainer> */}
          </>
        );
      }
      case 5: {
        return (
          <>
            <MobileAppBar title={t("returnPolicy")} hasBack onBack={() => setActiveStep(4)} />
            <ProfileContentContainer>
              <SupplierIReturn
                activeStep={activeStep}
                onChangeActiveStep={setActiveStep}
                address={{
                  ...supplierData?.data?.address,
                  phoneNumber: supplierData?.data?.address?.phoneNumber || supplierData?.data?.contactNumber
                }}
                warehouseAddress={supplierData?.data?.warehouseAddress}
                onProfileSaved={onProfileSaved}
              />
            </ProfileContentContainer>
          </>
        );
      }
      default:
        return null;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    activeStep,
    hasSupplier,
    isGetSupplierLoading,
    isProfileLoading,
    supplierData?.data?.address,
    supplierData?.data?.warehouseAddress,
    supplierDataFailed,
    supplierDataSucceeded,
    supplierProfile
  ]);

  return (
    <ProfileStepper
      hasSupplierShipping={hasSupplierShipping}
      defaultActiveStep={activeStep}
      hasData={!!supplierData?.data}
      userType={USER_TYPES.SUPPLIER}
      RenderContent={RenderContent}
      onChangeStep={setActiveStep}
      title={t("accountInfo")}
      isLegal={supplierData?.data?.identity?.isLegalPerson}
      name={supplierData?.data?.name}
      contactNumber={supplierData?.data?.contactNumber}
      profileStatus={supplierData?.data?.status}
    />
  );
}

export default SupplierProfile;
