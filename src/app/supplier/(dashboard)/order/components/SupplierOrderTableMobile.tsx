import "./SupplierOrderTable.css";

import { TOrderData } from "@/store/apps/order/types";
import { Box, Divider, Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import OrdersEmptyList from "./OrdersEmptyList";
import SupplierOrderFilters from "./SupplierOrderFilters/SupplierOrderFilters";
import useLanguage from "@/utils/hooks/useLanguage";
import useCurrency from "@/utils/hooks/useCurrency";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { routes } from "@/constants/routes/index";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import OrderStatus, { IOrderStatusItems } from "../[id]/orderDetail/OrderStatus";
import { ListInfiniteScroll } from "@/components/ui/ListInfiniteScroll";
import { calculateTotalCount } from "@/utils/helpers";
import { useEffect, useState } from "react";
import LoadingList from "@/components/containers/LoadingList/LoadingList";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import Image from "next/image";
import useModal from "@/utils/hooks/useModal";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import OrderStateFilter from "./orderState";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import Button from "@/components/ui/Button";
import BottomAction from "@/components/ui/bottomAction/BottomAction";

interface ISupplierOrderTableMobileProps {
  isLoading: boolean;
  isSupplierOrderError: boolean;
  ordersData?: TOrderData[];
  page: number;
  pageSize: number;
  totalCount: number;
  setPage: (val: number) => void;
}

function SupplierOrderTableMobile({
  page,
  setPage,
  pageSize,
  totalCount,
  isLoading,
  isSupplierOrderError,
  ordersData
}: ISupplierOrderTableMobileProps) {
  const { t } = useTranslation();
  const [{ renderDate }] = useLanguage();
  const makePath = useRoleBasePath();
  const [orders, setOrders] = useState<TOrderData[]>([]);
  const { showModal, hideModal } = useModal();

  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };
  const hasNextPage = calculateTotalCount({ pageSize, totalCount }) > page;
  const [downloadType, setDownloadType] = useState<"factor" | "label" | undefined>(undefined);

  const [checkedIds, setCheckedIds] = useState<string[]>([]);

  const handleCheckAllItems = (checked: boolean) => {
    const ids = orders
      // ?.filter(item => !checkedIds?.find(v => v === item?.id))
      ?.map(item => item.id) as string[];

    setCheckedIds(checked ? ids : []);
  };

  const handleCheckItem = (id: string) => {
    const existId = checkedIds?.find(item => item === id);

    if (existId) {
      setCheckedIds(prev => prev?.filter(item => item !== id));
    } else {
      setCheckedIds(prev => [...prev, id]);
    }
  };

  const handleShowModal = () => {
    showModal({
      body: (
        <div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-v2-content-primary text-body4-medium">{t("supplierOrder.orderMenu")}</span>
            </div>
            <Icon icon="iconamoon:close-light" className="size-5 cursor-pointer text-gray-400" onClick={hideModal} />
          </div>

          <div className="flex flex-col gap-1 items-start pt-4 ">
            <CustomButton
              color="info"
              className="whitespace-nowrap !text-v2-content-primary !text-body4-medium !px-0 hover:!border-transparent"
              startIcon={<Icon icon="solar:printer-outline" className="size-4" />}
              onClick={() => {
                setDownloadType("factor");
                hideModal();
              }}
            >
              {t("supplierOrder.printGroupFactor")}
            </CustomButton>

            <Divider orientation="horizontal" variant="fullWidth" flexItem className="bg-v2-border-primary" />

            <CustomButton
              color="info"
              className="whitespace-nowrap !text-v2-content-primary !text-body4-medium !px-0 hover:!border-transparent"
              startIcon={<Icon icon="solar:printer-outline" className="size-4" />}
              onClick={() => {
                setDownloadType("label");
                hideModal();
              }}
            >
              {t("supplierOrder.printGroupLabel")}
            </CustomButton>
          </div>
        </div>
      ),
      modalProps: {
        showCloseIcon: false
      }
    });
  };

  useEffect(() => {
    if (ordersData?.length && !isLoading) {
      if (page === 1) setOrders(ordersData);
      else setOrders(prevOrders => [...prevOrders, ...ordersData]);
    } else setOrders([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ordersData, isLoading]);

  return (
    <>
      <MobileAppBar
        className="!h-[96px] !justify-start pt-5"
        RenderComponent={
          <div className="relative">
            <div className="flex justify-between">
              <div className="flex items-center gap-1">
                {downloadType && (
                  <Icon
                    icon="tabler:arrow-right"
                    className="size-4 cursor-pointer"
                    onClick={() => {
                      setDownloadType(undefined);
                      setCheckedIds([]);
                    }}
                  />
                )}
                <span className="text-body2-medium text-v2-content-primary">{t("supplierOrder.orders")}</span>
                <span className="text-body2-medium text-v2-content-tertiary">({totalCount})</span>
              </div>

              {!downloadType && !!ordersData?.length && (
                <div className="flex items-center gap-1.5 cursor-pointer" onClick={handleShowModal}>
                  <Icon icon="flowbite:dots-vertical-outline" className="size-4 text-v2-content-secondary" />
                  <span className="text-v2-content-secondary text-body2-medium">{t("supplierOrder.more")}</span>
                </div>
              )}
            </div>

            <div className="absolute -bottom-14 left-0 w-full overflow-x-auto z-[999] ">
              <OrderStateFilter totalCount={totalCount} />
            </div>
          </div>
        }
      />
      <Box className="supplier-order-table-wrapper-mobile">
        <SupplierOrderFilters
          RenderStartAdornment={
            downloadType === "factor" &&
            !!checkedIds?.length &&
            checkedIds?.length === ordersData?.length && (
              <div className="flex flex-col gap-1.5 items-center rounded-lg p-2 bg-v2-surface-info border border-v2-border-primary">
                <div className=" flex items-center gap-1.5">
                  <Icon icon="gg:check-r" className="size-4 text-v2-content-tertiary" />
                  <span className="text-v2-content-tertiary text-body3-regular">
                    {t("order.downloadFactor.itemSelected", { number: checkedIds?.length })}
                  </span>
                </div>
                <span className="text-v2-content-on-info text-body3-medium">
                  {t("order.downloadFactor.selectAll", { number: totalCount })}
                </span>
              </div>
            )
          }
        />
        <Box mt={1}>
          {isLoading ? (
            <Box className="supplier-order-table-container-mobile">
              <LoadingList rowCount={6} columnCount={6} />
            </Box>
          ) : !orders?.length && !isLoading ? (
            <div className="w-full flex items-center justify-center min-h-[50vh]">
              <OrdersEmptyList />
            </div>
          ) : isSupplierOrderError ? (
            <Box
              width="100%"
              minHeight="50vh"
              display="flex"
              justifyContent="center"
              alignItems="center"
              className="supplier-order-table-container-mobile"
            >
              <Typography>{t("errors.somethingWentWrong")}</Typography>
            </Box>
          ) : (
            <>
              {!!downloadType && (
                <div className="flex items-center justify-between mb-5 mt-5">
                  <div className="flex items-center gap-2">
                    <CustomCheckbox
                      className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
                      indeterminate={!!checkedIds?.length && checkedIds?.length !== ordersData?.length}
                      onChange={(_e, checked) => handleCheckAllItems(checked)}
                      checked={!!checkedIds?.length && checkedIds?.length === ordersData?.length}
                    />

                    <span className="text-v2-content-primary text-body3-medium whitespace-nowrap">
                      {t("selectAll")}
                    </span>

                    {!!checkedIds?.length && (
                      <span className="text-body3-medium text-v2-content-tertiary whitespace-nowrap">
                        ({t("selected")} {checkedIds?.length})
                      </span>
                    )}
                  </div>

                  {!!checkedIds?.length && (
                    <BottomAction containerClassName="!z-[999999]">
                      <div className="px-6 w-full pt-2">
                        <Button
                          variant="primary"
                          className="w-full"
                          startAdornment={
                            <Icon
                              icon="solar:download-square-outline"
                              className="size-5 text-v2-content-on-action-hover-1"
                            />
                          }
                        >
                          {downloadType === "factor" ? t("order.downloadFactors") : t("order.downloadLabels")}
                        </Button>
                      </div>
                    </BottomAction>
                  )}
                </div>
              )}

              <Stack gap={1}>
                {orders?.map(item => {
                  const totalQuantities = item?.lineItems?.reduce(
                    (accumulator, currentValue) => accumulator + currentValue?.quantity,
                    0
                  );

                  const lineItemImageCount = item?.lineItems?.filter(i => i?.product?.cover?.url)?.length;

                  return (
                    <div className="flex items-start gap-2" key={item.id}>
                      {!!downloadType && (
                        <CustomCheckbox
                          className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
                          onChange={() => handleCheckItem(item?.id)}
                          checked={!!checkedIds?.length && checkedIds?.some(v => v === item?.id)}
                        />
                      )}

                      <Link className="p-4 bg-cards rounded-lg flex-1" href={`${makePath(routes.order)}/${item.id}`}>
                        <div className="flex items-center gap-1 justify-between">
                          <div className="flex items-center gap-1">
                            <Icon icon="solar:square-alt-arrow-left-bold" className="text-v2-content-primary size-4" />
                            <span className="text-body3-medium text-v2-content-primary">{item?.orderNumber}</span>
                          </div>
                          {!!item?.state && (
                            <OrderStatus
                              id={item?.state as IOrderStatusItems["id"]}
                              title={t(`supplierOrder.orderStateItems.${item?.state?.toLowerCase()}`)}
                            />
                          )}
                        </div>
                        <div className="flex items-center text-caption-medium gap-1.5 text-v2-content-secondary pb-2 border-b border-b-v2-border-secondary">
                          <span>{item?.customer?.firstName || "" + item?.customer?.lastName}</span>
                          <span className="text-v2-content-primary">.</span>
                          <span>
                            {totalQuantities} {t("commodity")}
                          </span>
                          <span className="text-v2-content-primary">.</span>
                          <span> {!!item.createdAt && renderDate(item?.createdAt, "hh:mm - YYYY/MM/DD")}</span>
                        </div>

                        <div className="pt-3 flex items-center justify-between">
                          <div className="flex items-center gap-0.5">
                            <span>{t("supplierOrder.totalPaid")} : </span>
                            <span>{renderPrice(item?.totalPrice)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              {item?.lineItems?.map(lItem => (
                                <>
                                  {!!lItem?.product?.cover?.url && (
                                    <div className="relative rounded-[4px] overflow-hidden">
                                      <Image
                                        key={lItem?.id}
                                        src={lItem?.product?.cover?.url}
                                        alt={lItem?.product?.cover?.alt}
                                        width={25}
                                        height={25}
                                        style={{
                                          borderRadius: "4px"
                                        }}
                                      />
                                      {lineItemImageCount && lineItemImageCount >= 3 && (
                                        <div
                                          style={{
                                            background: "rgba(0, 0, 0, 0.4)"
                                          }}
                                          className="text-body4-medium absolute w-full h-full top-0 left-0 flex items-center justify-center"
                                        >
                                          <span className="text-v2-content-on-action-hover-1">
                                            {lineItemImageCount}+
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </>
                              ))}
                            </div>
                            <Link href={`${makePath(routes.order)}/${item.id}`}>
                              <Icon icon="solar:alt-arrow-left-outline" className="text-v2-content-primary size-4" />
                            </Link>
                          </div>
                        </div>
                      </Link>
                    </div>
                  );
                })}
              </Stack>
            </>
          )}
        </Box>
        {hasNextPage && orders?.length >= 10 && (
          <ListInfiniteScroll hasNextPage={hasNextPage} fetchNextPage={() => setPage(page + 1)} />
        )}
      </Box>
    </>
  );
}

export default SupplierOrderTableMobile;
