import React from "react";
import { Box, Stack, Typography, Link, Divider } from "@mui/material";
import { Icon } from "@iconify/react";
import clsx from "clsx";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useTranslation } from "react-i18next";
import useCurrency from "@/utils/hooks/useCurrency";
import { TOrderData } from "@/store/apps/order/types";
import OrderDetailRow from "./OrderDetailRow";
import { ensureUrlScheme } from "@/utils/helpers";

// import "./OrderSummary.css";
import OrderDetailTable from "./OrderDetailTable";
import OrderStatus, { IOrderStatusItems } from "./OrderStatus";
import DownloadInvoice from "@/components/containers/orders/DownloadInvoice";
import useLanguage from "@/utils/hooks/useLanguage";
import useLocations from "@/utils/hooks/useLocations";
import { useParams } from "next/navigation";
import { twMerge } from "tailwind-merge";
import useClipboard from "@/utils/hooks/useClipboard";

interface IMobileOrderSummaryProps {
  supplierOrder?: { data: TOrderData };
  handleTrackingCode: () => void;
  subTotal: number;
  vat: number;
  shippingCost: number;
  isSupplierOrderFetching: boolean;
}

const MobileOrderDetail = ({
  supplierOrder,
  handleTrackingCode,
  subTotal,
  vat,
  shippingCost,
  isSupplierOrderFetching
}: IMobileOrderSummaryProps) => {
  const [{ renderDate }] = useLanguage();
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };
  const { getLocation } = useLocations();
  const params = useParams();
  const id = params?.id as string;
  const { isCopied, copyToClipboard } = useClipboard();

  return (
    <>
      <div className="flex flex-col flex-wrap bg-cards rounded-lg p-4">
        <div className="flex gap-3 items-center text-h4-bold text-v2-content-primary flex-wrap justify-between">
          <div className="flex items-center">
            <span className="whitespace-nowrap">{t("supplierOrder.orderSummary.orderNumber")} : </span>
            <span className="mr-1">{supplierOrder?.data?.orderNumber ?? ""}</span>#
          </div>
          <div>
            {!!supplierOrder?.data?.state && (
              <OrderStatus
                id={supplierOrder?.data?.state as IOrderStatusItems["id"]}
                title={t(`supplierOrder.orderStateItems.${supplierOrder?.data?.state?.toLowerCase()}`)}
              />
            )}
          </div>
        </div>

        <div className="flex items-center gap-2 mt-4">
          <Icon icon="solar:calendar-outline" className="size-4" />
          {!!supplierOrder?.data?.createdAt && (
            <div className="flex items-center gap-2 ">
              <span className="text-body3-medium text-v2-content-tertiary">
                {t("supplierOrder.orderSummary.orderAt")} :{" "}
              </span>
              <span className="text-body3-medium text-v2-content-tertiary">
                {!!supplierOrder?.data?.createdAt && renderDate(supplierOrder?.data?.createdAt, "hh:mm - YYYY/MM/DD")}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between mt-4">
          {supplierOrder?.data?.state === "Done" && (
            <div
              onClick={handleTrackingCode}
              className="flex items-center gap-1.5 text-v2-content-primary cursor-pointer"
            >
              <Icon icon="solar:scanner-outline" className="size-3.5 text-v2-content-primary" />
              <span className="text-body4-medium text-v2-content-primary">{t("order.trackingCode")}</span>
            </div>
          )}

          <div className="flex items-center gap-1.5 xmd:px-5 xmd:py-2.5 text-purple-500">
            <Icon icon="solar:printer-outline" className="size-4 text-purple-500" />
            <span className="text-body4-medium text-v2-content-on-info">{t("downloadLabel")}</span>
          </div>

          <Divider orientation="vertical" variant="middle" flexItem className="bg-gray-50" />

          {supplierOrder?.data?.state === "Done" &&
            (supplierOrder?.data?.paymentState === "Paid" || supplierOrder?.data?.paymentState === "Captured") && (
              <Divider orientation="vertical" variant="middle" flexItem className="bg-gray-50" />
            )}

          {(supplierOrder?.data?.paymentState === "Paid" || supplierOrder?.data?.paymentState === "Captured") && (
            <DownloadInvoice
              orderId={id}
              orderNumber={supplierOrder?.data?.orderNumber}
              printCustomerTitle={t("supplierOrder.orderSummary.printCustomerInvoice")}
              printSellerTitle={t("supplierOrder.orderSummary.printSellerInvoice")}
            />
          )}
        </div>
      </div>

      <div className="bg-cards rounded-lg p-4 mt-3">
        <div>
          <span className="text-v2-content-primary text-body2-medium font-semibold">
            {t("supplierOrder.orderSummary.statuses")}
          </span>

          <div className="flex items-center justify-between pb-3 border-b border-b-v2-border-secondary mt-6">
            <span className="text-body4-regular text-v2-content-primary">
              {t("supplierOrder.orderSummary.orderState")}
            </span>

            {!!supplierOrder?.data?.state && (
              <OrderStatus
                id={supplierOrder?.data?.state as IOrderStatusItems["id"]}
                title={t(`supplierOrder.orderStateItems.${supplierOrder?.data?.state?.toLowerCase()}`)}
              />
            )}
          </div>

          <div className="flex items-center justify-between py-3 border-b border-b-v2-border-secondary">
            <span className="text-body4-regular text-v2-content-primary">
              {t("supplierOrder.orderSummary.paymentState")}
            </span>

            {!!supplierOrder?.data?.paymentState && (
              <OrderStatus
                id={supplierOrder?.data?.paymentState}
                title={t(`supplierOrder.paymentStateItems.${supplierOrder?.data?.paymentState?.toLowerCase()}`)}
              />
            )}
          </div>

          <div className="flex items-center justify-between pt-3 ">
            <span className="text-body4-regular text-v2-content-primary">
              {t("supplierOrder.orderSummary.deliveryState")}
            </span>

            {!!supplierOrder?.data?.shippingState && (
              <OrderStatus
                id={supplierOrder?.data?.shippingState}
                title={t(`supplierOrder.deliveryStateItems.${supplierOrder?.data?.shippingState?.toLowerCase()}`)}
              />
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 mt-3">
        <div className=" rounded-lg p-4 bg-cards">
          <div className="flex items-center gap-2 pb-6 border-b border-b-v2-border-secondary">
            <Icon icon="solar:cart-3-outline" className="size-6" />
            <span className="text-v2-content-primary text-body2-bold font-semibold">
              {t("supplierOrder.orderSummary.addedOrder")}
            </span>
          </div>

          <div className="pt-3">
            {supplierOrder?.data?.lineItems?.map((item, index) => (
              <OrderDetailRow
                item={item}
                shipmentDetail={supplierOrder?.data?.shipmentDetails?.find(
                  sItem => sItem?.supplierId === item?.supplierId
                )}
                key={item.orderId}
                isLastItem={(supplierOrder?.data?.lineItems?.length || 0) - 1 === index}
                lineItems={supplierOrder?.data?.lineItems}
              />
            ))}
          </div>
        </div>

        {/* <div className=" rounded-lg p-4 bg-cards mt-3">
          <div>
            <span className="text-v2-content-primary text-body2-medium font-semibold">
              {t("supplierOrder.orderSummary.customerInfo")}
            </span>
          </div>

          <div className="flex items-center justify-between mt-6 pb-3 border-b border-b-v2-border-secondary">
            <div className="flex items-center gap-1">
              <Icon icon="carbon:user-avatar" width={15} height={15} />
              <span className="text-body4-regular text-v2-content-primary">
                {t("supplierOrder.orderSummary.customer")}
              </span>
            </div>

            <span className="text-body4-regular text-v2-content-primary">
              {" "}
              {supplierOrder?.data?.customer?.firstName} {supplierOrder?.data?.customer?.lastName}
            </span>
          </div>

          <div className="flex items-center justify-between  py-3 border-b border-b-v2-border-secondary">
            <div className="flex items-center gap-1">
              <Icon icon="mage:email" width={15} height={15} />
              <span className="text-body4-regular text-v2-content-primary">
                {t("supplierOrder.orderSummary.email")}
              </span>
            </div>
            <span className="text-body4-regular text-v2-content-primary">
              {" "}
              {supplierOrder?.data?.customer?.email ?? "-"}{" "}
            </span>
          </div>

          <div className="flex items-center justify-between pt-3">
            <div className="flex items-center gap-1">
              <Icon icon="solar:outgoing-call-outline" width={15} height={15} />{" "}
              <span className="text-body4-regular text-v2-content-primary">
                {t("supplierOrder.orderSummary.phone")}
              </span>
            </div>
            <span className="text-body4-regular text-v2-content-primary">
              {supplierOrder?.data?.customer?.phoneNumber ? supplierOrder?.data?.customer?.phoneNumber : "-"}{" "}
            </span>
          </div>
        </div> */}

        <div className=" rounded-lg p-4 mt-3 bg-cards">
          <div className="flex items-center gap-2 ">
            <Icon icon="solar:card-outline" className="size-6" />
            <span className="text-v2-content-primary text-body2-bold font-semibold">
              {t("supplierOrder.orderSummary.payInfo.title")}
            </span>
            <div className="mr-2">
              {!!supplierOrder?.data?.paymentState && (
                <OrderStatus
                  id={supplierOrder?.data?.paymentState}
                  title={t(`supplierOrder.paymentStateItems.${supplierOrder?.data?.paymentState?.toLowerCase()}`)}
                />
              )}
            </div>
          </div>
          <p className="text-body4-regular text-v2-content-primary mt-2">
            {t("supplierOrder.orderSummary.payInfo.subtitle")}
          </p>

          <div className="mt-6">
            <div className="flex justify-between">
              <div className="flex flex-col gap-1">
                <span className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("supplierOrder.orderSummary.grandTotal")}
                </span>
                <span className="flex-1 text-caption-medium text-v2-content-tertiary">
                  {supplierOrder?.data?.lineItems?.reduce((sum, item) => sum + item?.quantity, 0)}
                </span>
              </div>
              <div className="flex-1 text-body3-medium text-v2-content-primary text-end ">{renderPrice(subTotal)}</div>
            </div>

            <div className="flex justify-between mt-6">
              <div className="flex flex-col gap-1">
                <span className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("supplierOrder.orderSummary.discount")}
                </span>
                <span className="flex-1 text-caption-medium text-v2-content-tertiary ">-</span>
              </div>
              <div className="flex-1 text-body3-medium text-v2-content-primary text-end">-</div>
            </div>

            <div className="flex justify-between mt-6">
              <div className="flex flex-col gap-1">
                <span className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("supplierOrder.orderSummary.shippingCost")}
                </span>
                <span className="flex-1 text-caption-medium text-v2-content-tertiary ">
                  {t("supplierOrder.orderSummary.freeIfBiggerThan2mil")}
                </span>
              </div>
              <div className="flex-1 text-body3-medium text-v2-content-primary text-end">
                {renderPrice(shippingCost)}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between mt-6 bg-v2-surface-info rounded-lg px-3 py-2">
            <p className="flex-1 text-body3-medium !font-bold text-v2-content-primary">
              {t("supplierOrder.orderSummary.finalTotalPrice")}
            </p>
            <p className="flex-1" />
            <p className="flex-1 text-end text-body3-medium !font-bold text-v2-content-primary">
              {renderPrice(supplierOrder?.data?.totalPrice)}
            </p>
          </div>
        </div>
      </div>

      <div className=" w-full ">
        <div className=" rounded-lg p-4 bg-cards mt-3">
          <div className="flex items-center justify-between">
            <span className="text-v2-content-primary text-body2-medium font-semibold">
              {t("supplierOrder.orderSummary.customerPostAddress")}
            </span>
          </div>

          <div className="flex items-center gap-1 pb-3 border-b border-b-v2-border-secondary mt-6">
            <Icon icon="carbon:user-avatar" width={15} height={15} />
            <span className="text-body4-regular text-v2-content-primary">
              {supplierOrder?.data?.customer?.firstName || ""} {supplierOrder?.data?.customer?.lastName || ""}{" "}
            </span>
          </div>

          {/* <div className="flex items-center ">
                <Icon icon="carbon:user-avatar" width={15} height={15} />
                <span>
                  {t("supplierOrder.orderSummary.cityState") + supplierOrder?.data?.shippingAddress?.state ||
                    getLocation(supplierOrder?.data?.shippingAddress?.locationId ?? "")?.parent?.name ||
                    "-"}
                </span>
              </div> */}

          <div className="flex items-center gap-1 py-3 ">
            <Icon icon="solar:map-arrow-square-outline" width={15} height={15} />
            <span className="text-body4-regular text-v2-content-primary">
              {t("supplierOrder.orderSummary.cityState")}{" "}
              {getLocation(supplierOrder?.data?.shippingAddress?.locationId ?? "")?.parent?.name || "-"}
            </span>
          </div>

          <div className="mb-2">
            <span className="text-body4-regular text-v2-content-primary">
              {t("supplierOrder.orderSummary.city")}:{" "}
              {getLocation(supplierOrder?.data?.shippingAddress?.locationId ?? "")?.name || "-"}
            </span>
          </div>

          <div>
            <span className="text-body4-regular text-v2-content-primary">
              {supplierOrder?.data?.shippingAddress?.address1 || ""}
            </span>
          </div>

          <div className="flex items-center justify-between mt-2">
            <span className="text-body4-regular text-gray-600">{t("order.phoneNumber")}</span>
            <span className="text-body4-regular text-v2-content-primary">
              {supplierOrder?.data?.shippingAddress?.phoneNumber
                ? supplierOrder?.data?.shippingAddress?.phoneNumber
                : "-"}
            </span>
          </div>

          <div className="flex items-center justify-between mt-1">
            <span className="text-body4-regular text-gray-600">{t("supplier.profile.zip")}</span>
            <span className="text-body4-regular text-v2-content-primary">
              {supplierOrder?.data?.shippingAddress?.zip ? supplierOrder?.data?.shippingAddress?.zip : "-"}
            </span>
          </div>
        </div>

        <div className="bg-cards rounded-lg p-4 mt-3">
          <div className="flex items-center justify-between">
            <span className="text-v2-content-primary text-body2-medium font-semibold">{t("order.shippingInfo")}</span>
            {supplierOrder?.data?.state === "Done" && (
              <Icon
                onClick={handleTrackingCode}
                icon="solar:pen-2-outline"
                className="size-4 text-gray-400 cursor-pointer"
              />
            )}
          </div>

          <div className="mt-6">
            {supplierOrder?.data?.shipmentDetails?.map(item => (
              <div key={item?.orderId}>
                <div className="flex items-center justify-between pb-3 border-b border-b-v2-border-secondary">
                  <div className="flex items-center gap-1">
                    <Icon icon="solar:square-alt-arrow-left-outline" width={15} height={15} />
                    <span className="text-body4-regular text-v2-content-primary"> {t("order.shippingType")}</span>
                  </div>
                  <span className="text-body4-medium text-v2-content-primary"> {t("order.shippingType")}</span>
                </div>

                <div className="flex items-center justify-between pt-3 ">
                  <div className="flex items-center gap-1">
                    <Icon icon="solar:scanner-outline" width={15} height={15} />
                    <span className="text-body4-regular text-v2-content-primary"> {t("order.trackingCode")}</span>
                  </div>
                  <Link
                    target="_blank"
                    rel="noopener noreferrer"
                    href={ensureUrlScheme(item?.trackingUrl)}
                    className="py-0.5 px-1 rounded-full flex items-center gap-2 text-v2-content-on-info bg-v2-surface-info"
                  >
                    <span className="text-caption-medium">{t("supplierOrder.orderSummary.tracking")}</span>
                    <Icon
                      icon="solar:round-alt-arrow-left-bold"
                      width={14}
                      height={14}
                      className="text-v2-content-on-info"
                    />
                  </Link>
                </div>

                <div className="flex items-center gap-1 mt-4 mr-auto w-fit">
                  <span className="text-body4-medium text-v2-content-primary">{item?.trackingCode}</span>
                  <Icon
                    icon={isCopied ? "flat-color-icons:checkmark" : "icon-park-outline:copy"}
                    className={twMerge(isCopied ? "size-4 mb-1" : "size-3.5 ", "text-gray-600 cursor-pointer")}
                    onClick={e => {
                      e.stopPropagation();
                      copyToClipboard(item?.trackingCode);
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileOrderDetail;
