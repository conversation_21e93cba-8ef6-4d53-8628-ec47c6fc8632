import React, { useEffect, useState, forwardRef, useRef } from "react";
import { AdapterDateFnsJalali } from "@mui/x-date-pickers/AdapterDateFnsJalali";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import {
  StaticDateTimePicker,
  PickerChangeHandlerContext,
  StaticDatePicker,
  StaticDateTimePickerSlots
} from "@mui/x-date-pickers";
import { ClickAwayListener, Popper } from "@mui/material";
import { ICalendarProps } from "./types"; // Ensure this matches your prop types
import { CustomToolbar, formatDate, maxDate, weekDaysTitle } from "./utils";
import { useTranslation } from "react-i18next";
import CustomButton from "../CustomButton/CustomButton";
import "./CustomDatePicker.css";
import { Icon } from "@iconify/react";
import Input from "../inputs/Input";
import { isDate, isNaN } from "lodash";

const DatePicker = forwardRef<HTMLInputElement, ICalendarProps>(
  (
    {
      label,
      error,
      helperText,
      hasTime,
      name,
      optional,
      onChange,
      onBlur,
      inputProps,
      slots,
      placeholder,
      wrapperStyle,
      value: defaultValue,
      ...restProps
    },
    ref
  ) => {
    const validDate = isNaN(new Date(defaultValue).getTime());
    const initialValue = defaultValue === "0001-01-01T00:00:00Z" || validDate ? "" : defaultValue;

    const { t } = useTranslation();
    const [tempValue, setTempValue] = useState<Date | null>(null);
    const [value, setValue] = useState<Date | null>(initialValue ? new Date(initialValue) : null);
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [open, setOpen] = useState<boolean>(false);
    const containerRef = useRef<HTMLDivElement>(null);
    // const [currentView, setCurrentView] = useState<DateView>("day");

    // const handleViewChange = (view: DateView) => {
    //   setCurrentView(view);
    // };

    const handleChange = (newValue: Date | null, context: PickerChangeHandlerContext<any>) => {
      setValue(newValue);
      onChange?.(newValue ? newValue.toISOString() : null);
      setAnchorEl(null);
      setOpen(false);
    };

    const handleChangeDateAndTime = (newValue: Date | null, context: PickerChangeHandlerContext<any>) => {
      setTempValue(newValue);
    };

    useEffect(() => {
      if (initialValue) setValue(new Date(initialValue));
    }, [initialValue]);

    const handleOpen = (event: React.FocusEvent<HTMLInputElement>) => {
      setAnchorEl(event.currentTarget);
      setOpen(prev => !prev);
      if (!value && (restProps?.maxDate || maxDate)) {
        const val = restProps?.maxDate || maxDate;
        setValue(val);
        onChange?.(val ? val.toISOString() : null);
      }
    };

    const handleClose = () => {
      setOpen(false);
    };

    const handleClickAway = (event: any) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        handleClose();
      }
    };

    return (
      <div style={wrapperStyle}>
        <LocalizationProvider dateAdapter={AdapterDateFnsJalali}>
          {hasTime ? (
            <>
              <ClickAwayListener onClickAway={handleClickAway}>
                <div ref={containerRef}>
                  <Input
                    {...inputProps}
                    value={value == null ? "" : formatDate(value, "yyyy/MM/dd:HH:mm")}
                    onFocus={handleOpen}
                    autoComplete="off"
                    readOnly
                    endAdornment={<Icon icon="solar:calendar-outline" className="size-4 text-v2-content-tertiary " />}
                    optional={optional}
                    name={name}
                    error={error}
                    helperText={helperText}
                    type="text"
                    label={label}
                    placeholder={placeholder}
                    onBlur={onBlur}
                  />

                  <Popper
                    open={open}
                    anchorEl={anchorEl}
                    placement="bottom"
                    className="!z-[99999999]"
                    popperOptions={{
                      placement: "bottom",
                      modifiers: [
                        {
                          name: "offset",
                          options: {
                            offset: [-20, 0]
                          }
                        }
                      ]
                    }}
                  >
                    <StaticDateTimePicker
                      {...restProps}
                      value={value}
                      onChange={handleChangeDateAndTime}
                      ampm={false}
                      ref={ref}
                      slots={{
                        ...(slots as unknown as StaticDateTimePickerSlots<any>),
                        toolbar: CustomToolbar as any,
                        actionBar: () => (
                          <div
                            className="flex items-center gap-2 pb-4 px-4"
                            style={{
                              gridColumn: "2"
                            }}
                          >
                            <CustomButton fullWidth onClick={handleClose}>
                              {t("cancel")}
                            </CustomButton>
                            <CustomButton
                              fullWidth
                              onClick={() => {
                                setValue(tempValue);
                                onChange?.(tempValue ? tempValue.toISOString() : null);
                                handleClose();
                              }}
                            >
                              {t("ok")}
                            </CustomButton>
                          </div>
                        )
                      }}
                      sx={{
                        boxShadow: "0 9px 17.5px rgb(0,0,0,0.05)"
                      }}
                      className="rounded-lg"
                      maxDate={restProps?.maxDate || new Date()}
                      dayOfWeekFormatter={weekday => weekDaysTitle[weekday.getDay()]}
                    />
                  </Popper>
                </div>
              </ClickAwayListener>
            </>
          ) : (
            <>
              <ClickAwayListener onClickAway={handleClickAway}>
                <div ref={containerRef}>
                  <Input
                    {...inputProps}
                    value={value == null ? "" : formatDate(value, "yyyy/MM/dd")}
                    onFocus={handleOpen}
                    autoComplete="off"
                    readOnly
                    endAdornment={
                      <Icon
                        icon="solar:calendar-outline"
                        className="size-4 pointer-events-none text-v2-content-primary"
                      />
                    }
                    optional={optional}
                    name={name}
                    error={error}
                    helperText={helperText}
                    type="text"
                    label={label}
                    placeholder={placeholder}
                    onBlur={onBlur}
                  />

                  <Popper
                    open={open}
                    anchorEl={anchorEl}
                    placement="bottom"
                    className="!z-[99999999] "
                    popperOptions={{
                      placement: "bottom",
                      modifiers: [
                        {
                          name: "offset",
                          options: {
                            offset: [-15, 0]
                          }
                        }
                      ]
                    }}
                    // popperOptions={{
                    //   modifiers: [
                    //     {
                    //       // options: {
                    //       offset: [-24, -24]
                    //       // }
                    //     }
                    //   ]
                    // }}
                  >
                    <StaticDatePicker
                      sx={{
                        boxShadow: "0 9px 17.5px rgb(0,0,0,0.05)"
                      }}
                      slots={{
                        ...slots,
                        toolbar: undefined,
                        actionBar: () => null
                        // calendarHeader: CustomCalendarHeader
                      }}
                      ref={ref}
                      // onViewChange={handleViewChange}
                      // view={currentView}
                      // views={["year", "month", "day"]}
                      // onViewChange={handleViewChange}
                      // view={currentView}
                      // views={["year", "month", "day"]}
                      className="rounded-lg"
                      onChange={handleChange}
                      value={value}
                      maxDate={restProps?.maxDate}
                      dayOfWeekFormatter={weekday => weekDaysTitle[weekday.getDay()]}
                    />
                  </Popper>
                </div>
              </ClickAwayListener>
            </>
          )}
        </LocalizationProvider>
      </div>
    );
  }
);

DatePicker.displayName = "DatePicker";

export default DatePicker;
