import React, { useState } from "react";
import { twMerge } from "tailwind-merge";

export type TSwitchButtonsProps = {
  initialValue?: string;
  items: { label: string; id: string }[];
  onChange: (id: string) => void;
};

function SwitchButtons(props: TSwitchButtonsProps) {
  const { items, onChange, initialValue } = props;
  const [internaVal, setInternaVal] = useState(initialValue);

  const handleOnChange = (id: string) => {
    setInternaVal(id);
    onChange(id);
  };

  return (
    <div className="p-1.5 bg-v2-surface-thertiary rounded-lg flex items-center gap-2 select-none">
      {items?.map(item => (
        <div
          key={item?.id}
          className={twMerge(
            "px-4 py-2.5 rounded-lg cursor-pointer font-medium text-sm",
            internaVal === item?.id
              ? "bg-v2-surface-action text-v2-content-on-action-1"
              : "bg-transparent text-v2-content-primary"
          )}
          onClick={() => handleOnChange(item?.id)}
        >
          {item?.label}
        </div>
      ))}
    </div>
  );
}

export default SwitchButtons;
