import React from "react";
import { twMerge } from "tailwind-merge";

export type TCustomTabsFilledProps = {
  className?: string;
  items: { value: string; title: string }[];
  value?: string;
  onChange?: (value: string) => void;
};

function CustomTabsFilled({ className, items, value, onChange }: TCustomTabsFilledProps) {
  return (
    <div className={twMerge("flex items-center cursor-pointer gap-3", className)}>
      {items?.map(item => (
        <div
          key={item.value}
          className={twMerge(
            "rounded-full text-[13px] leading-5 font-medium px-4 py-2",
            item.value === value
              ? "bg-v2-surface-action text-v2-content-on-action-1"
              : "bg-v2-surface-thertiary text-v2-content-primary"
          )}
          onClick={() => onChange?.(item.value)}
        >
          {item.title}
        </div>
      ))}
    </div>
  );
}

export default CustomTabsFilled;
