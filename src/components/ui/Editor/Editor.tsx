import "./LexicalEditor.css";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import ToolbarPlugin from "./plugins/ToolbarPlugin";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { TableCellNode, TableNode, TableRowNode } from "@lexical/table";
import { ListItemNode, ListNode } from "@lexical/list";
import { CodeHighlightNode, CodeNode } from "@lexical/code";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import { ParagraphNode } from "lexical";
import { $convertFromMarkdownString, $convertToMarkdownString, TRANSFORMERS } from "@lexical/markdown";
import { $generateHtmlFromNodes, $generateNodesFromDOM } from "@lexical/html";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import ListMaxIndentLevelPlugin from "./plugins/ListMaxIndentLevelPlugin";
import CodeHighlightPlugin from "./plugins/CodeHighlightPlugin";
import AutoLinkPlugin from "./plugins/AutoLinkPlugin";
import { $getRoot } from "lexical";
import { ReactNode, useMemo, useEffect } from "react";
import InputHelper from "../CustomFormHelperText/InputHelper";

// Custom plugin to handle HTML initialization and change events
function HtmlPlugin({
  initialHtml,
  useHtmlMode,
  onChange
}: {
  initialHtml?: string;
  useHtmlMode: boolean;
  onChange?: (value: string) => void;
}) {
  const [editor] = useLexicalComposerContext();

  // Initialize HTML content
  useEffect(() => {
    if (useHtmlMode && initialHtml && isHTMLString(initialHtml)) {
      editor.update(() => {
        const parser = new DOMParser();
        const dom = parser.parseFromString(initialHtml, "text/html");
        const nodes = $generateNodesFromDOM(editor, dom);
        const root = $getRoot();
        root.clear();
        root.append(...nodes);
      });
    }
  }, [editor, initialHtml, useHtmlMode]);

  // Handle change events
  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        if (useHtmlMode) {
          const htmlString = $generateHtmlFromNodes(editor);
          onChange?.(htmlString);
        } else {
          const markdown = $convertToMarkdownString(TRANSFORMERS);
          onChange?.(markdown);
        }
      });
    });
  }, [editor, useHtmlMode, onChange]);

  return null;
}

function isHTMLString(str?: string) {
  if (!str) return;

  const htmlTagPattern = /<[^>]+>/;
  return htmlTagPattern.test(str);
}

function Placeholder({ placeholder }: { placeholder: string }) {
  return <div className="editor-placeholder">{placeholder}</div>;
}

interface LexicalEditorProps {
  initialValue?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  isRTL?: boolean;
  error?: boolean;
  helperText?: ReactNode;
  preserveHtml?: boolean; // New prop to control HTML preservation
}

export default function Editor({
  autoFocus,
  initialValue,
  onChange,
  placeholder,
  error,
  helperText,
  isRTL = true,
  preserveHtml = false
}: LexicalEditorProps) {
  // Determine if we should use HTML mode
  const useHtmlMode = Boolean(preserveHtml || isHTMLString(initialValue));

  const initialConfig = useMemo(
    () => ({
      editorState: () => {
        if (!initialValue) return "";

        if (useHtmlMode && isHTMLString(initialValue)) {
          // For HTML content, we'll set it after editor initialization
          return "";
        } else if (!useHtmlMode && initialValue) {
          // Parse Markdown content
          return $convertFromMarkdownString(initialValue, TRANSFORMERS);
        }

        return "";
      },

      namespace: "MyEditor",
      onError: (error: Error) => {
        console.error("Lexical error:", error);
      },
      nodes: [
        HeadingNode,
        QuoteNode,
        ListItemNode,
        ListNode,
        TableCellNode,
        TableNode,
        TableRowNode,
        CodeHighlightNode,
        CodeNode,
        AutoLinkNode,
        LinkNode,
        ParagraphNode // Ensure ParagraphNode is included here
      ]
    }),
    [initialValue, useHtmlMode]
  );

  return (
    <>
      <LexicalComposer initialConfig={initialConfig}>
        <div className={`editor-container ${isRTL ? "rtl" : "ltr"} ${error ? "!border-v2-content-on-error-2" : ""}`}>
          <ToolbarPlugin />
          <div className="editor-inner">
            <RichTextPlugin
              contentEditable={<ContentEditable className="editor-input" autoComplete="off" />}
              placeholder={placeholder ? <Placeholder placeholder={placeholder} /> : <></>}
              ErrorBoundary={LexicalErrorBoundary}
            />
            <HistoryPlugin />
            {/* <TreeViewPlugin /> */}
            <AutoFocusPlugin />
            <CodeHighlightPlugin />
            <ListPlugin />
            <LinkPlugin />
            <AutoLinkPlugin />
            <ListMaxIndentLevelPlugin maxDepth={7} />
            <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
            {/* <SetEditorRefPlugin editorRef={editorRef} /> */}
            {/* <InitialValuePlugin initialValue={initialValue} isRTL /> */}

            {autoFocus && <AutoFocusPlugin />}

            <HtmlPlugin
              initialHtml={useHtmlMode ? initialValue : undefined}
              useHtmlMode={useHtmlMode}
              onChange={onChange}
            />
          </div>
        </div>
      </LexicalComposer>
      {helperText && (
        <InputHelper error={error} className="mt-1">
          {helperText as string}
        </InputHelper>
      )}
    </>
  );
}
