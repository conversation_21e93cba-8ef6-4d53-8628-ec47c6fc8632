import "./LexicalEditor.css";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import LexicalErrorBoundary from "@lexical/react/LexicalErrorBoundary";
import ToolbarPlugin from "./plugins/ToolbarPlugin";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { TableCellNode, TableNode, TableRowNode } from "@lexical/table";
import { ListItemNode, ListNode } from "@lexical/list";
import { CodeHighlightNode, CodeNode } from "@lexical/code";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import { ParagraphNode } from "lexical";
import { $convertFromMarkdownString, $convertToMarkdownString, TRANSFORMERS } from "@lexical/markdown";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import ListMaxIndentLevelPlugin from "./plugins/ListMaxIndentLevelPlugin";
import CodeHighlightPlugin from "./plugins/CodeHighlightPlugin";
import AutoLinkPlugin from "./plugins/AutoLinkPlugin";
import { EditorState } from "lexical";
import { ReactNode, useMemo } from "react";
import InputHelper from "../CustomFormHelperText/InputHelper";
import TurndownService from "turndown";

function isHTMLString(str?: string) {
  if (!str) return;

  const htmlTagPattern = /<[^>]+>/;
  return htmlTagPattern.test(str);
}

function Placeholder({ placeholder }: { placeholder: string }) {
  return <div className="editor-placeholder">{placeholder}</div>;
}

interface LexicalEditorProps {
  initialValue?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  isRTL?: boolean;
  error?: boolean;
  helperText?: ReactNode;
}

export default function Editor({
  autoFocus,
  initialValue,
  onChange,
  placeholder,
  error,
  helperText,
  isRTL = true
}: LexicalEditorProps) {
  const turndownService = new TurndownService();

  turndownService.addRule("customHeadings", {
    filter: ["h1", "h2", "h3", "h4", "h5", "h6"],
    replacement: function (content: string, node: HTMLElement) {
      const level = node.nodeName.charAt(1);
      return `${"#".repeat(level as any)} ${content}\n\n`;
    } as any
  });

  const markdown = isHTMLString(initialValue) ? turndownService.turndown(initialValue ?? "") : initialValue;

  const initialConfig = useMemo(
    () => ({
      editorState: () => (markdown ? $convertFromMarkdownString(markdown, TRANSFORMERS) : ""),

      namespace: "MyEditor",
      onError: (error: Error) => {
        console.error("Lexical error:", error);
      },
      nodes: [
        HeadingNode,
        QuoteNode,
        ListItemNode,
        ListNode,
        TableCellNode,
        TableNode,
        TableRowNode,
        CodeHighlightNode,
        CodeNode,
        AutoLinkNode,
        LinkNode,
        ParagraphNode // Ensure ParagraphNode is included here
      ]
    }),
    [markdown]
  );

  const handleChange = (editorState: EditorState) => {
    editorState.read(() => {
      const markdown = $convertToMarkdownString(TRANSFORMERS);
      onChange?.(markdown);
    });
  };

  return (
    <>
      <LexicalComposer initialConfig={initialConfig}>
        <div className={`editor-container ${isRTL ? "rtl" : "ltr"} ${error ? "!border-v2-content-on-error-2" : ""}`}>
          <ToolbarPlugin />
          <div className="editor-inner">
            <RichTextPlugin
              contentEditable={<ContentEditable className="editor-input" autoComplete="off" />}
              placeholder={placeholder ? <Placeholder placeholder={placeholder} /> : <></>}
              ErrorBoundary={LexicalErrorBoundary}
            />
            <HistoryPlugin />
            {/* <TreeViewPlugin /> */}
            <AutoFocusPlugin />
            <CodeHighlightPlugin />
            <ListPlugin />
            <LinkPlugin />
            <AutoLinkPlugin />
            <ListMaxIndentLevelPlugin maxDepth={7} />
            <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
            {/* <SetEditorRefPlugin editorRef={editorRef} /> */}
            {/* <InitialValuePlugin initialValue={initialValue} isRTL /> */}

            {autoFocus && <AutoFocusPlugin />}

            <OnChangePlugin onChange={handleChange} />
          </div>
        </div>
      </LexicalComposer>
      {helperText && (
        <InputHelper error={error} className="mt-1">
          {helperText as string}
        </InputHelper>
      )}
    </>
  );
}
