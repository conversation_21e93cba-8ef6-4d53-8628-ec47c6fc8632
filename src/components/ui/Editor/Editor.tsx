import "./LexicalEditor.css";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import ToolbarPlugin from "./plugins/ToolbarPlugin";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { TableCellNode, TableNode, TableRowNode } from "@lexical/table";
import { ListItemNode, ListNode } from "@lexical/list";
import { CodeHighlightNode, CodeNode } from "@lexical/code";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import {
  ParagraphNode,
  TextNode,
  LineBreakNode,
  $getRoot,
  $createParagraphNode,
  $createTextNode,
  ElementNode,
  LexicalNode,
  NodeKey,
  SerializedElementNode,
  Spread
} from "lexical";
import { $convertFromMarkdownString, $convertToMarkdownString, TRANSFORMERS } from "@lexical/markdown";
import { $generateHtmlFromNodes, $generateNodesFromDOM } from "@lexical/html";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import ListMaxIndentLevelPlugin from "./plugins/ListMaxIndentLevelPlugin";
import CodeHighlightPlugin from "./plugins/CodeHighlightPlugin";
import AutoLinkPlugin from "./plugins/AutoLinkPlugin";
import { EditorState, KEY_ENTER_COMMAND, COMMAND_PRIORITY_LOW } from "lexical";
import { ReactNode, useMemo, useEffect } from "react";
import InputHelper from "../CustomFormHelperText/InputHelper";

function isHTMLString(str?: string) {
  if (!str) return false;
  const htmlTagPattern = /<[^>]+>/;
  return htmlTagPattern.test(str);
}

function Placeholder({ placeholder }: { placeholder: string }) {
  return <div className="editor-placeholder">{placeholder}</div>;
}

// Custom Node to preserve inline styles
export type SerializedStyledNode = Spread<
  {
    styles: Record<string, string>;
    className?: string;
    tag: string;
  },
  SerializedElementNode
>;

export class StyledNode extends ElementNode {
  __styles: Record<string, string>;
  __className?: string;
  __tag: string;

  static getType(): string {
    return "styled";
  }

  constructor(tag: string, styles: Record<string, string> = {}, className?: string, key?: NodeKey) {
    super(key);
    this.__tag = tag;
    this.__styles = styles;
    this.__className = className;
  }

  static clone(node: StyledNode): StyledNode {
    return new StyledNode(node.__tag, { ...node.__styles }, node.__className, node.__key);
  }

  createDOM(): HTMLElement {
    const element = document.createElement(this.__tag);

    // Apply inline styles
    Object.entries(this.__styles).forEach(([property, value]) => {
      element.style.setProperty(property, value);
    });

    // Apply class name
    if (this.__className) {
      element.className = this.__className;
    }

    return element;
  }

  updateDOM(): boolean {
    return false;
  }

  static importDOM(): Record<string, any> | null {
    return {
      div: () => ({ conversion: convertStyledElement, priority: 1 }),
      span: () => ({ conversion: convertStyledElement, priority: 1 }),
      h1: () => ({ conversion: convertStyledElement, priority: 1 }),
      h2: () => ({ conversion: convertStyledElement, priority: 1 }),
      h3: () => ({ conversion: convertStyledElement, priority: 1 }),
      h4: () => ({ conversion: convertStyledElement, priority: 1 }),
      h5: () => ({ conversion: convertStyledElement, priority: 1 }),
      h6: () => ({ conversion: convertStyledElement, priority: 1 }),
      p: () => ({ conversion: convertStyledElement, priority: 1 }),
      ul: () => ({ conversion: convertStyledElement, priority: 1 }),
      li: () => ({ conversion: convertStyledElement, priority: 1 })
    };
  }

  exportDOM(): { element: HTMLElement } {
    const element = this.createDOM();
    return { element };
  }

  static importJSON(serializedNode: SerializedStyledNode): StyledNode {
    const { tag, styles, className } = serializedNode;
    return new StyledNode(tag, styles, className);
  }

  exportJSON(): SerializedStyledNode {
    return {
      ...super.exportJSON(),
      tag: this.__tag,
      styles: this.__styles,
      className: this.__className,
      type: "styled",
      version: 1
    };
  }
}

function convertStyledElement(domNode: HTMLElement): { node: StyledNode } | null {
  const tag = domNode.tagName.toLowerCase();
  const styles: Record<string, string> = {};
  const className = domNode.className;

  // Extract inline styles
  if (domNode.style.length > 0) {
    for (let i = 0; i < domNode.style.length; i++) {
      const property = domNode.style.item(i);
      styles[property] = domNode.style.getPropertyValue(property);
    }
  }

  const node = new StyledNode(tag, styles, className);
  return { node };
}

export function $createStyledNode(tag: string, styles: Record<string, string> = {}, className?: string): StyledNode {
  return new StyledNode(tag, styles, className);
}

export function $isStyledNode(node: LexicalNode | null | undefined): node is StyledNode {
  return node instanceof StyledNode;
}

// Simple Enter Key Handler Plugin
function EnterKeyPlugin() {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return editor.registerCommand(
      KEY_ENTER_COMMAND,
      event => {
        const selection = editor.getEditorState().read(() => {
          return editor.getEditorState()._selection;
        });

        if (selection) {
          editor.update(() => {
            const root = $getRoot();
            const paragraph = $createParagraphNode();
            root.append(paragraph);
            paragraph.select();
          });
          return true;
        }
        return false;
      },
      COMMAND_PRIORITY_LOW
    );
  }, [editor]);

  return null;
}

// Custom plugin to handle HTML import with preserved styles
function HTMLImportPlugin({ htmlContent }: { htmlContent?: string }) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (!htmlContent || !isHTMLString(htmlContent)) return;

    editor.update(() => {
      const root = $getRoot();
      root.clear();

      // Create a temporary DOM element to parse HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, "text/html");

      // Import nodes from the parsed HTML with our custom conversion
      const nodes = $generateNodesFromDOM(editor, doc);
      root.append(...nodes);
    });
  }, [editor, htmlContent]);

  return null;
}

interface LexicalEditorProps {
  initialValue?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  isRTL?: boolean;
  error?: boolean;
  helperText?: ReactNode;
}

export default function Editor({
  autoFocus,
  initialValue,
  onChange,
  placeholder,
  error,
  helperText,
  isRTL = true
}: LexicalEditorProps) {
  const initialConfig = useMemo(
    () => ({
      namespace: "MyEditor",
      onError: (error: Error) => {
        console.error("Lexical error:", error);
      },
      nodes: [
        HeadingNode,
        QuoteNode,
        ListItemNode,
        ListNode,
        TableCellNode,
        TableNode,
        TableRowNode,
        CodeHighlightNode,
        CodeNode,
        AutoLinkNode,
        LinkNode,
        ParagraphNode,
        TextNode,
        LineBreakNode,
        StyledNode // Add our custom styled node
      ],
      theme: {
        // Keep existing theme
      }
    }),
    []
  );

  const handleChange = (editorState: EditorState) => {
    editorState.read(() => {
      const root = $getRoot();
      const textContent = root.getTextContent();

      // For now, just return the text content to fix Enter key
      // You can enhance this later to generate proper HTML
      onChange?.(textContent);
    });
  };

  return (
    <>
      <LexicalComposer initialConfig={initialConfig}>
        <div className={`editor-container ${isRTL ? "rtl" : "ltr"} ${error ? "!border-v2-content-on-error-2" : ""}`}>
          <ToolbarPlugin />
          <div className="editor-inner">
            <RichTextPlugin
              contentEditable={
                <ContentEditable
                  className="editor-input"
                  autoComplete="off"
                  style={{
                    outline: "none",
                    padding: "12px",
                    minHeight: "200px"
                  }}
                />
              }
              placeholder={placeholder ? <Placeholder placeholder={placeholder} /> : <></>}
              ErrorBoundary={LexicalErrorBoundary}
            />
            <HistoryPlugin />
            <AutoFocusPlugin />
            <CodeHighlightPlugin />
            <ListPlugin />
            <LinkPlugin />
            <AutoLinkPlugin />
            <ListMaxIndentLevelPlugin maxDepth={7} />
            <HTMLImportPlugin htmlContent={isHTMLString(initialValue) ? initialValue : undefined} />

            {autoFocus && <AutoFocusPlugin />}

            <OnChangePlugin onChange={handleChange} />
          </div>
        </div>
      </LexicalComposer>
      {helperText && (
        <InputHelper error={error} className="mt-1">
          {helperText as string}
        </InputHelper>
      )}
    </>
  );
}
