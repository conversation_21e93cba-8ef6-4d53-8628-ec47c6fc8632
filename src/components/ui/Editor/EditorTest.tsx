import React, { useState } from "react";
import Editor from "./Editor";

const EditorTest = () => {
  const [htmlValue, setHtmlValue] = useState(
    '<p style="color: red; font-weight: bold;">Hello <span style="background-color: yellow;">World</span>!</p>'
  );
  const [markdownValue, setMarkdownValue] = useState("# Hello World\n\nThis is **bold** text.");
  const [preserveHtmlValue, setPreserveHtmlValue] = useState("Some regular text that will be treated as HTML");
  const [inlineCssValue, setInlineCssValue] = useState(
    '<div style="padding: 10px; border: 2px solid blue; background-color: lightblue;">This text has inline CSS styles that should be preserved and displayed correctly.</div>'
  );

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h1>Lexical Editor Tests</h1>

      <div style={{ marginBottom: "40px" }}>
        <h2>1. HTML with Inline CSS (Auto-detected)</h2>
        <p>This editor should automatically detect HTML and preserve it:</p>
        <Editor initialValue={htmlValue} onChange={setHtmlValue} placeholder="Enter HTML content..." />
        <div style={{ marginTop: "10px", padding: "10px", backgroundColor: "#f5f5f5", borderRadius: "4px" }}>
          <strong>Output:</strong>
          <pre style={{ whiteSpace: "pre-wrap", fontSize: "12px" }}>{htmlValue}</pre>
        </div>
      </div>

      <div style={{ marginBottom: "40px" }}>
        <h2>2. Markdown Content (Traditional mode)</h2>
        <p>This editor should work in traditional Markdown mode:</p>
        <Editor initialValue={markdownValue} onChange={setMarkdownValue} placeholder="Enter Markdown content..." />
        <div style={{ marginTop: "10px", padding: "10px", backgroundColor: "#f5f5f5", borderRadius: "4px" }}>
          <strong>Output:</strong>
          <pre style={{ whiteSpace: "pre-wrap", fontSize: "12px" }}>{markdownValue}</pre>
        </div>
      </div>

      <div style={{ marginBottom: "40px" }}>
        <h2>3. Inline CSS Test (Issue Fix)</h2>
        <p>This tests that inline CSS styles are properly detected and preserved:</p>
        <Editor
          initialValue={inlineCssValue}
          onChange={setInlineCssValue}
          placeholder="Enter HTML with inline CSS..."
        />
        <div style={{ marginTop: "10px", padding: "10px", backgroundColor: "#f5f5f5", borderRadius: "4px" }}>
          <strong>Output:</strong>
          <pre style={{ whiteSpace: "pre-wrap", fontSize: "12px" }}>{inlineCssValue}</pre>
        </div>
      </div>

      <div style={{ marginBottom: "40px" }}>
        <h2>4. Force HTML Mode</h2>
        <p>This editor is forced to use HTML mode with preserveHtml prop:</p>
        <Editor
          initialValue={preserveHtmlValue}
          onChange={setPreserveHtmlValue}
          placeholder="Enter content (will be treated as HTML)..."
          preserveHtml={true}
        />
        <div style={{ marginTop: "10px", padding: "10px", backgroundColor: "#f5f5f5", borderRadius: "4px" }}>
          <strong>Output:</strong>
          <pre style={{ whiteSpace: "pre-wrap", fontSize: "12px" }}>{preserveHtmlValue}</pre>
        </div>
      </div>

      <div style={{ marginBottom: "40px" }}>
        <h2>Test HTML Content Examples</h2>
        <button
          onClick={() =>
            setHtmlValue(
              '<div style="border: 2px solid blue; padding: 10px; margin: 5px;"><h3 style="color: green;">Styled Header</h3><p>This is a <strong style="color: red;">bold red</strong> text with <em style="background-color: lightblue;">italic blue background</em>.</p></div>'
            )
          }
          style={{
            marginRight: "10px",
            padding: "8px 16px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer"
          }}
        >
          Load Complex HTML
        </button>
        <button
          onClick={() =>
            setHtmlValue(
              '<p style="font-size: 18px; color: purple; text-align: center; text-decoration: underline;">Centered Purple Underlined Text</p>'
            )
          }
          style={{
            marginRight: "10px",
            padding: "8px 16px",
            backgroundColor: "#28a745",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer"
          }}
        >
          Load Styled Text
        </button>
        <button
          onClick={() =>
            setHtmlValue(
              '<ul style="list-style-type: none; padding: 0;"><li style="background-color: #ffebcd; margin: 5px; padding: 10px; border-left: 4px solid #ff6347;">Item 1</li><li style="background-color: #f0f8ff; margin: 5px; padding: 10px; border-left: 4px solid #4169e1;">Item 2</li></ul>'
            )
          }
          style={{
            padding: "8px 16px",
            backgroundColor: "#ffc107",
            color: "black",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer"
          }}
        >
          Load Styled List
        </button>
      </div>
    </div>
  );
};

export default EditorTest;
