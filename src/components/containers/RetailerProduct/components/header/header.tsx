import React from "react";
import { useTranslation } from "react-i18next";
import { useProductFilters } from "../Filters/useProductFilters";
import SwitchButtons from "@/components/ui/switchButtons/switchButtons";
import useBreadCrumbData from "../breadcrumb/useBreadCrumbData";
import { useMediaQuery } from "@mui/system";
import { Theme } from "@mui/material";

function Header({ totalDataCount = 0 }: { totalDataCount?: number }) {
  const { t } = useTranslation();
  const { setFilters, viewType } = useProductFilters();
  const breadCrumbData = useBreadCrumbData();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  if (isMobile) {
    return (
      <div className="flex items-center gap-1 justify-between">
        <div>
          <SwitchButtons
            initialValue={viewType}
            items={[
              { label: t("retailerProduct.showBasedOnProducts"), id: "products" },
              { label: t("retailerProduct.showBasedOnSuppliers"), id: "suppliers" }
            ]}
            onChange={v => setFilters({ viewType: v as any })}
          />
        </div>

        <div className="flex items-center gap-2">
          <div className="text-v2-content-primary text-[15px] font-semibold">{totalDataCount}</div>
          <div className="text-v2-content-tertiary text-[13px] font-normal">{t("retailerProduct.searchResult")}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="text-v2-content-primary text-lg font-semibold">
        {breadCrumbData?.at(-1)?.name || t("retailerProduct.marketplace")}
      </div>
      <div className="flex justify-between">
        <div className="flex items-center gap-4">
          <div className="text-v2-content-primary text-[13px] font-medium">{t("retailerProduct.showBasedOn")}</div>
          <SwitchButtons
            initialValue={viewType}
            items={[
              { label: t("retailerProduct.showBasedOnProducts"), id: "products" },
              { label: t("retailerProduct.showBasedOnSuppliers"), id: "suppliers" }
            ]}
            onChange={v => setFilters({ viewType: v as any })}
          />
        </div>
        <div className="flex items-center gap-2">
          <div className="text-v2-content-primary text-[15px] font-semibold">{totalDataCount}</div>
          <div className="text-v2-content-tertiary text-[13px] font-normal">{t("retailerProduct.searchResult")}</div>
        </div>
      </div>
    </div>
  );
}

export default Header;
