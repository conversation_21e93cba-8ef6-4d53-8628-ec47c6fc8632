import CustomBreadcrumb from "@/components/ui/CustomBreadcrumb/CustomBreadcrumb";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import React from "react";
import { useTranslation } from "react-i18next";
import { useProductFilters } from "../Filters/useProductFilters";
import useBreadCrumbData from "./useBreadCrumbData";

function Breadcrumb() {
  const makePath = useRoleBasePath();
  const { t } = useTranslation();
  const { setFilters, resetAll } = useProductFilters();

  const breadCrumbData = useBreadCrumbData();

  const BCrumb = [
    {
      to: `${makePath(routes.product)}`,
      title: t("retailerProduct.marketplace"),
      className: "!text-v2-content-primary",
      onClick: resetAll
    },
    ...(breadCrumbData?.map((item, index) => ({
      title: item?.name,
      onClick: () => setFilters({ category: item?.id }),
      className: index + 1 === breadCrumbData?.length ? "!text-v2-content-tertiary" : "!text-v2-content-primary"
    })) || [])
  ];

  if (breadCrumbData?.length) {
    return (
      <div>
        <CustomBreadcrumb items={BCrumb} className="!p-0 !mb-0 " />
      </div>
    );
  }

  return null;
}

export default Breadcrumb;
