import React from "react";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import useBreadCrumbData from "../breadcrumb/useBreadCrumbData";
import { useProductFilters } from "../Filters/useProductFilters";
import useModal from "@/utils/hooks/useModal";
import MobileCategoriesActionSheet from "../Filters/MobileCategories";

function MobileCategories() {
  const { t } = useTranslation();
  const { hideModal, showModal } = useModal();
  const { filters, pagination, sorts: sortsStates, setFilters } = useProductFilters();

  const breadCrumbData = useBreadCrumbData();

  const handleClickMobileCategories = () => {
    showModal({
      body: ({ drawer }) => (
        <MobileCategoriesActionSheet
          closePopOver={hideModal}
          setFilterState={(k, v) => setFilters({ [k]: v }, { history: "push" })}
          defaultValue={filters?.category || undefined}
          calcFitHeight={drawer?.calcFitHeight}
        />
      )
    });
  };

  return (
    <div
      className="text-sm font-medium text-v2-content-primary py-[9px] px-3 rounded-lg border border-v2-border-primary flex items-center justify-between"
      onClick={handleClickMobileCategories}
    >
      {breadCrumbData?.at(-1)?.name || t("retailerProduct.allCategories")}
      <Icon icon="solar:alt-arrow-down-outline" width={16} height={16} className="text-v2-content-tertiary" />
    </div>
  );
}

export default MobileCategories;
