import "./ChatContent.css";

import React, { ForwardedRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import Avatar from "@mui/material/Avatar";
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import Typography from "@mui/material/Typography";

import { CircularProgress, Theme, useMediaQuery } from "@mui/material";
import { TChat } from "@/store/apps/retailer/types";
import useLanguage from "@/utils/hooks/useLanguage";
import { usePathname, useSearchParams } from "next/navigation";
import ChatNotFound from "./ChatNotFound";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { useGetConversationMessageQuery } from "@/store/apps/conversation";
import {
  ConversationMessagePayload,
  ConversationMessageResponse,
  ConversationPayload
} from "@/store/apps/conversation/types";

export type TSelectedChat = { isNew: boolean; supplier?: TChat; chat?: snakeCaseToCamelCase<ConversationPayload> };
interface ChatContentProps {
  selected?: TSelectedChat;
  chat?: snakeCaseToCamelCase<ConversationMessageResponse>;
  isChatLoading?: boolean;
}

export interface IChatContentRef {
  addNewMessage: (message: snakeCaseToCamelCase<ConversationMessagePayload>) => void;
}

export const RELOAD_CHAT = 5 * 1000;

const ChatContent = ({ selected }: ChatContentProps, ref: ForwardedRef<IChatContentRef>) => {
  const pathname = usePathname();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const searchParams = useSearchParams();
  const [{ timeDistance }] = useLanguage();
  const scrollRef = useRef<HTMLDivElement>(null);
  const mobileBottom = useRef<HTMLDivElement>(null);

  const chatId = searchParams?.get("chatId");

  const { data: chat, isLoading: isChatLoading } = useGetConversationMessageQuery(
    { id: chatId || "" },
    { skip: !chatId, pollingInterval: RELOAD_CHAT }
  );

  const [messages, setMessages] = useState<snakeCaseToCamelCase<ConversationMessagePayload>[]>([]);

  useImperativeHandle(
    ref,
    () => ({
      addNewMessage: message => setMessages(prev => prev.concat(message))
    }),
    []
  );

  useEffect(() => {
    if (chat?.data) {
      setMessages(chat?.data);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chat, pathname]);

  useEffect(() => {
    // Scroll to the bottom whenever messages change
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    if (mobileBottom.current) mobileBottom.current?.scrollIntoView({ behavior: "smooth" });
  }, [mobileBottom.current, messages]);

  const user = selected && selected?.chat ? selected.chat.partner : undefined;

  if (isChatLoading) {
    return (
      <Box display="flex" height="100%" justifyContent="center" alignItems="center" className="xmd:h-full h-[86vh]">
        <CircularProgress size={30} />
      </Box>
    );
  }

  if (isMobile) {
    return (
      <Box pt={2} pb={9} width="100%" className="overflow-auto  min-h-[100dvh] ">
        {!chat?.data?.length ? (
          <ChatNotFound />
        ) : (
          messages &&
          messages.map(item => {
            return (
              <Box key={item.id}>
                {!item.ours ? (
                  <Box display="flex" flexDirection="row-reverse" gap={1.5}>
                    <Avatar alt={user?.name} src={user?.avatar} className="w-10 h-10" />
                    <Box>
                      {item.sendAt ? (
                        <Typography className="text-caption-regular xmd:text-gray-400 text-gray-999 mb-1 text-left">
                          {timeDistance(new Date(item.sendAt)) + " "} , {item?.user?.name}
                        </Typography>
                      ) : null}
                      <Box
                        mb={2}
                        className="py-2 px-2.5 bg-v2-surface-thertiary text-v2-content-secondary rounded-[5px]"
                        sx={{
                          wordBreak: "break-word"
                        }}
                      >
                        {item.content}
                      </Box>
                    </Box>
                  </Box>
                ) : (
                  <Box mb={1} display="flex">
                    <Box display="flex" flexDirection={"column"}>
                      {item.sendAt ? (
                        <Typography className="text-caption-regular xmd:text-gray-400 text-gray-999 mb-1">
                          {timeDistance(new Date(item.sendAt)) + " "}
                        </Typography>
                      ) : null}

                      <Box
                        mb={1}
                        className="bg-v2-surface-action-light text-v2-content-secondary text-xs py-2 px-2.5 rounded-[5px]"
                        sx={{
                          wordBreak: "break-word"
                        }}
                      >
                        {item.content}
                      </Box>
                    </Box>
                  </Box>
                )}
              </Box>
            );
          })
        )}
        <div className="h-1 opacity-0" ref={mobileBottom} />
      </Box>
    );
  }

  return (
    <Box flex={1}>
      <Box display="flex" flexDirection="column" className=" h-full">
        <Box>
          <Box display="flex" alignItems="center" px={2} py={1}>
            {/* <Box id="sx-chatcontent-312">
                <IconMenu2 stroke={1.5} onClick={toggleChatSidebar} />
              </Box> */}
            <ListItem dense disableGutters className="gap-3">
              <Avatar alt={user?.name} src={user?.avatar} />
              <ListItemText
                classes={{
                  secondary: "text-xs text-gray-400"
                }}
                primary={<Typography className="text-gray-999 text-[15px] font-medium">{user?.name}</Typography>}
                secondary={
                  !!selected?.chat?.messages?.length
                    ? selected?.chat?.messages[selected?.chat?.messages?.length - 1]?.content
                    : undefined
                }
              />
            </ListItem>
          </Box>
          <Divider />
        </Box>
        <Box className=" flex-1 overflow-auto h-full max-h-[60dvh]" ref={scrollRef}>
          {!chat?.data?.length ? (
            <ChatNotFound />
          ) : (
            <Box p={2} width="100%" height="100%">
              {messages &&
                messages.map(item => {
                  return (
                    <Box key={item.id}>
                      {!item.ours ? (
                        <Box display="flex" flexDirection="row-reverse" gap={1.5}>
                          <Avatar alt={user?.name} src={user?.avatar} className="sx-chatcontent-340" sizes="40px" />
                          <Box>
                            {item.sendAt ? (
                              <Typography className="text-caption-regular xmd:text-gray-400 text-gray-999 mb-1 text-left">
                                {timeDistance(new Date(item.sendAt)) + " "} , {item?.user?.name}
                              </Typography>
                            ) : null}
                            <Box
                              mb={2}
                              className="py-2 px-2.5 bg-v2-surface-thertiary text-v2-content-secondary rounded-[5px] text-xs"
                              sx={{
                                wordBreak: "break-word"
                              }}
                            >
                              {item.content}
                            </Box>
                          </Box>
                        </Box>
                      ) : (
                        <Box mb={1} display="flex">
                          <Box display="flex" flexDirection={"column"}>
                            {item.sendAt ? (
                              <Typography className="text-caption-regular xmd:text-gray-400 text-gray-999 mb-1">
                                {timeDistance(new Date(item.sendAt)) + " "}
                              </Typography>
                            ) : null}

                            <Box
                              mb={1}
                              className="bg-v2-surface-action-light text-v2-content-secondary text-xs py-2 px-2.5 rounded-[5px]"
                              sx={{
                                wordBreak: "break-word"
                              }}
                            >
                              {item.content}
                            </Box>
                          </Box>
                        </Box>
                      )}
                    </Box>
                  );
                })}
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default React.forwardRef(ChatContent);

// <Box flexDirection="column" display="flex" flexGrow={1} p={2} pb={1} pt={1}>
//   <Box flexGrow={1} display="flex" flexDirection="column" alignItems="center" justifyContent="center">
//     {/* {isChatLoading ? <CircularProgress size={20} /> : t("chats.noMessage")}
//     {!isChatLoading && <Button>{t("chats.selectChat")}</Button>} */}
//     <Image src="/images/svgs/chat-notfound.svg"/>
//   </Box>
// </Box>
