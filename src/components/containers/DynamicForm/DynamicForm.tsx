/* eslint-disable @typescript-eslint/no-unused-vars */
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import NumberInput from "@/components/ui/inputs/NumberInput";
import { LANGUAGES } from "@/constants/localization";
import { FormElementData, InputType } from "@/store/apps/meta/types";
import { TSupplierIntegrationData } from "@/store/apps/supplier/types";
import useCurrency from "@/utils/hooks/useCurrency";
import useLanguage from "@/utils/hooks/useLanguage";
import { Box, CircularProgress, FormControl, Grid } from "@mui/material";
import { Field, Form, Formik, FormikHelpers } from "formik";
import { isNaN } from "lodash";
import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import {
  CheckBoxFieldComponent,
  FileFieldComponent,
  getValidationSchema,
  NumberFieldComponent,
  PasswordFieldComponent,
  RadioFieldComponent,
  TextFieldComponent
} from "./utils";
import { Icon } from "@iconify/react";

interface IStoreDynamicFormProps {
  submitButtonText?: string;
  actionContainerClassName?: string;
  renderBottom?: () => React.JSX.Element;
  hasExtraFields?: boolean;
  gridColumn?: number;
  configForm: { [key: string]: FormElementData };
  isLoading: boolean;
  isSubmitting?: boolean;
  storeDataSingle?: TSupplierIntegrationData;
  isStoreSingleLoading?: boolean;
  handleSubmit: (
    values: {
      [key: string]: any;
    },
    {
      setFieldError
    }: FormikHelpers<{
      [key: string]: any;
    }>
  ) => Promise<void>;
  onCancel?: () => void;
  cancelButtonText?: string;
}

const DynamicForm = ({
  hasExtraFields = false,
  renderBottom,
  isSubmitting,
  configForm,
  gridColumn,
  handleSubmit,
  isLoading,
  storeDataSingle,
  isStoreSingleLoading,
  submitButtonText,
  cancelButtonText,
  actionContainerClassName,
  onCancel
}: IStoreDynamicFormProps) => {
  const { t } = useTranslation();
  const [currentLang] = useLanguage();
  const [_selected, _selectCurrency, curOptions] = useCurrency();

  const currencyOptions = curOptions?.map(item => ({
    label: item.name,
    id: item.id
  }));

  const languageOptions = LANGUAGES?.map(item => ({
    label: item.flagname,
    id: item.value
  }));

  const CustomCurrencySelect = ({ field, form, onChange, ...props }: any) => (
    <CustomAutocomplete<(typeof currencyOptions)[0]>
      {...field}
      {...props}
      value={currencyOptions?.find(item => item?.id === field.value)}
      options={currencyOptions}
      onChange={(e, value) => props?.setFieldValue(field.name, value?.id)}
    />
  );

  const CustomLanguageSelect = ({ field, form, onChange, ...props }: any) => (
    <CustomAutocomplete<(typeof languageOptions)[0]>
      {...field}
      {...props}
      value={languageOptions?.find(item => item?.id === field.value)}
      options={languageOptions}
      onChange={(e, value) => props?.setFieldValue(field.name, value?.id)}
    />
  );

  const initialValues =
    Object.keys(configForm).reduce(
      (acc, key) => {
        acc[key] = storeDataSingle?.config?.[key] || "";
        return acc;
      },
      {} as { [key: string]: any }
    ) || {};

  const extraValues = {
    preferences: storeDataSingle?.preferences || {},
    wholesalePrice: !storeDataSingle?.wholesalePrice ? "Customer" : "Supplier",
    adjustmentPercentage: storeDataSingle?.adjustmentPercentage
  };

  const finalInitialValues = hasExtraFields
    ? ({ ...initialValues, ...extraValues } as { [key: string]: any })
    : initialValues;

  const finalConfigForm = configForm
    ? ({
        ...configForm,
        adjustmentPercentage: {
          min: 0,
          max: 100,
          type: "percent"
        }
      } as unknown as { [key: string]: FormElementData })
    : {};

  const validationSchema = getValidationSchema(finalConfigForm, hasExtraFields);

  const getComponent = (type: InputType) => {
    switch (type) {
      case InputType.Text:
      case InputType.Email:
      case InputType.URL:
        return TextFieldComponent;
      case InputType.Tel:
      case InputType.Number:
        return NumberFieldComponent;
      case InputType.Password:
        return PasswordFieldComponent;
      case InputType.Radio:
        return RadioFieldComponent;
      case InputType.Checkbox:
        return CheckBoxFieldComponent;
      case InputType.File:
        return FileFieldComponent;
      case InputType.Currency:
        return CustomCurrencySelect;
      case InputType.Locale:
        return CustomLanguageSelect;
      default:
        return TextFieldComponent;
    }
  };

  const onSubmit = async (values: { [key: string]: any }, formikHelpers: FormikHelpers<{ [key: string]: any }>) => {
    handleSubmit(values, formikHelpers);
  };

  if (isLoading || isStoreSingleLoading) {
    return (
      <Box className="flex items-center justify-center">
        <CircularProgress />
      </Box>
    );
  }
  return (
    <Formik
      enableReinitialize
      initialValues={finalInitialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ setFieldValue, handleBlur, handleChange, errors, touched, values }) => (
        <Form>
          <Grid container spacing={2}>
            {Object.keys(configForm).map(key => (
              <Grid item xs={12} md={gridColumn || configForm[key].width} key={key}>
                <Field
                  name={key}
                  component={getComponent(configForm[key].type)}
                  label={configForm[key].label.find(l => l.iso === currentLang?.value)?.text || key}
                  placeholder={
                    configForm?.[key]?.placeholder
                      ? configForm[key]?.placeholder?.find(l => l.iso === currentLang?.value)?.text
                      : configForm[key].label.find(l => l.iso === currentLang?.value)?.text || key
                  }
                  requiredStar={
                    configForm[key]?.required ||
                    (configForm[key]?.requiredIfNotPresent && !values[configForm[key]?.requiredIfNotPresent || ""])
                  }
                  optional={
                    !(
                      configForm[key]?.required ||
                      (configForm[key]?.requiredIfNotPresent && !values[configForm[key]?.requiredIfNotPresent || ""])
                    )
                  }
                  setFieldValue={setFieldValue}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  addon={configForm[key].addon}
                  addonSide={configForm[key].addonSide}
                  addonType={configForm[key].addonType}
                  append={configForm[key].append}
                  prepend={configForm[key].prepend}
                  error={touched[key] && Boolean(errors[key])}
                  helperText={touched[key] && errors[key]}
                  isLtr={configForm[key].direction === "ltr"}
                />
                {!!configForm[key].hint?.length && (
                  <p className="text-gray-400 text-body3-medium mt-0.5">
                    {configForm[key].hint?.find(l => l.iso === currentLang?.value)?.text || key}
                  </p>
                )}
              </Grid>
            ))}

            {!!hasExtraFields && (
              <>
                <Grid item xs={12} md={12}>
                  <span className="text-body2-medium font-semibold text-v2-content-primary">
                    {t("store.priceType")}
                  </span>
                  <FormControl id="sx-supplierinfo-17762">
                    <div className="flex items-center xmd:gap-8 gap-4">
                      <CustomRadio
                        checked={values?.wholesalePrice === "Supplier"}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value="Supplier"
                        label={t("store.partPrice")}
                        name="wholesalePrice"
                        labelClassName="!ml-0"
                      />
                      <CustomRadio
                        checked={values?.wholesalePrice === "Customer"}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value="Customer"
                        label={t("store.wholePrice")}
                        name="wholesalePrice"
                        labelClassName="!ml-0"
                      />
                    </div>
                  </FormControl>
                </Grid>

                {/* {!values?.wholesalePrice && ( */}
                <Grid item xs={12} md={12} marginBottom={0}>
                  <NumberInput
                    id="adjustmentPercentage"
                    name="adjustmentPercentage"
                    autoComplete="off"
                    onBlur={handleBlur}
                    label={t("store.adjustmentPercentage")}
                    value={values?.adjustmentPercentage || ""}
                    placeholder={t("store.adjustmentPercentage")}
                    endAdornment={<span className="text-caption-regular text-gray-400">{t("percent")}</span>}
                    onChange={e => {
                      if (e?.target?.value === undefined || isNaN(e?.target?.value)) return;

                      setFieldValue("adjustmentPercentage", Number(e?.target?.value));
                    }}
                    error={touched.adjustmentPercentage && Boolean(errors.adjustmentPercentage)}
                    helperText={
                      (!!touched?.adjustmentPercentage && (errors?.adjustmentPercentage as string)) || undefined
                    }
                  />
                </Grid>

                <Grid item xs={12} md={12} marginTop={0}>
                  <div className="bg-v2-surface-info flex items-center gap-1.5 p-2.5 rounded-md">
                    <Icon icon="solar:info-circle-outline" className="size-5 shrink-0" />
                    <span className="text-body4-medium text-gray-999">{t("store.alertPrice")}</span>
                  </div>
                </Grid>

                <Grid item xs={12} md={12} marginTop={1}>
                  <h2 className="text-body2-bold !font-semibold text-gray-999">{t("synchronization")}</h2>
                </Grid>

                <Grid item xs={12} md={12}>
                  <div className="flex items-start gap-2">
                    <CustomSwitch
                      id="preferences.syncOrders"
                      name="preferences.syncOrders"
                      checked={values?.preferences?.syncOrders}
                      label=""
                      labelClassName="!mr-0 !ml-0"
                      onChange={(e, checked) => setFieldValue("preferences.syncOrders", checked)}
                    />
                    <div className="flex flex-col ">
                      <span className="text-body4-medium text-gray-999">{t("store.syncOrdersTitle")}</span>
                      <span className="text-caption-medium text-gray-600">{t("store.syncOrdersSubtitle")}</span>
                    </div>
                  </div>
                </Grid>
                <Grid item xs={12} md={12}>
                  <div className="flex items-start gap-2">
                    <CustomSwitch
                      id="preferences.syncProducts"
                      name="preferences.syncProducts"
                      checked={values?.preferences?.syncProducts}
                      label=""
                      labelClassName="!mr-0 !ml-0"
                      onChange={(e, checked) => setFieldValue("preferences.syncProducts", checked)}
                    />
                    <div className="flex flex-col ">
                      <span className="text-body4-medium text-gray-999">{t("store.syncProductsTitle")}</span>
                      <span className="text-caption-medium text-gray-600">{t("store.syncProductsSubtitle")}</span>
                    </div>{" "}
                  </div>
                </Grid>

                {/* <Grid item xs={12} md={12}>
                  <div className="flex items-center gap-2">
                    <CustomSwitch
                      id="preferences.syncOrders"
                      name="preferences.syncOrders"
                      checked={values?.preferences?.syncOrders}
                      label=""
                      labelClassName="!mr-0 !ml-0"
                      onChange={(e, checked) => setFieldValue("preferences.syncOrders", checked)}
                    />
                    <span className="text-caption-medium text-gray-999">{t("store.syncOrders")}</span>
                  </div>
                </Grid>
                <Grid item xs={12} md={12}>
                  <div className="flex items-center gap-2">
                    <CustomSwitch
                      id="preferences.syncProducts"
                      name="preferences.syncProducts"
                      checked={values?.preferences?.syncProducts}
                      label=""
                      labelClassName="!mr-0 !ml-0"
                      onChange={(e, checked) => setFieldValue("preferences.syncProducts", checked)}
                    />
                    <span className="text-caption-medium text-gray-999">{t("store.syncProducts")}</span>
                  </div>
                </Grid> */}
                {/* )} */}
              </>
            )}
            <Grid item xs={12} md={12}>
              {renderBottom?.()}
            </Grid>

            <BottomAction
              saveButtonText={submitButtonText || t("saveChanges")}
              saveButtonProps={{
                type: "submit"
              }}
              cancelButtonText={cancelButtonText}
              cancelButtonProps={{
                classes: !!cancelButtonText ? "flex" : "hidden",
                onClick: () => {
                  onCancel?.();
                }
              }}
            />

            <Grid
              item
              xs={12}
              className={twMerge(
                cancelButtonText ? "xmd:flex hidden justify-between" : "xmd:flex hidden justify-end",
                actionContainerClassName
              )}
            >
              {!!cancelButtonText && (
                <CustomButton onClick={onCancel} color="secondary">
                  {cancelButtonText}
                </CustomButton>
              )}
              <CustomButton type="submit">
                {isSubmitting ? <CircularProgress color="info" size={26} /> : submitButtonText || t("saveChanges")}
              </CustomButton>
            </Grid>
          </Grid>
        </Form>
      )}
    </Formik>
  );
};

export default DynamicForm;
