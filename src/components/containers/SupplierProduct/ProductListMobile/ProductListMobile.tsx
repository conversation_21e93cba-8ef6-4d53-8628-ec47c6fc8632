import ExcelUploadModal from "@/components/forms/product-form/ProductFilters/ExcelUploadModal";
import ProductsFilter from "@/components/forms/product-form/ProductFilters/ProductFilters";
import { useProductFilters } from "@/components/forms/product-form/ProductFilters/useProductFilters";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import { ListInfiniteScroll } from "@/components/ui/ListInfiniteScroll";
import SupplierProductCardMobile from "@/components/ui/supplierProductCardMobile/supplierProductCardMobile";
import { routes } from "@/constants/routes/index";
import { useGetProductCategoryMapperCountQuery } from "@/store/apps/product";
import { IProductPayLoad } from "@/store/apps/product/types";
import { calculateTotalCount } from "@/utils/helpers";
import useCurrency from "@/utils/hooks/useCurrency";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { Box, CircularProgress, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ProductBulkChangePrice from "../ProductBulkChangePrice";
import ProductDeleteConfirmation from "../ProductDeleteConfirmation";
import ProductEmptyList from "../ProductEmptyList";
import BulkMenu from "./components/bulkMenu";
import Header from "./components/header";
import HeaderMenu from "./components/HeaderMenu";
import ProductMenu from "./components/productMenu";
import StickyActionsFooter from "./components/StickyActionsFooter";

interface IProductListMobileProps {
  isLoading: boolean;
  isSupplierOrderError: boolean;
  productsData?: IProductPayLoad[];
  page: number;
  pageSize: number;
  totalCount: number;
  setPage: (val: number) => void;
  shouldRenderFilters: boolean;
}

function ProductListMobile({
  page,
  setPage,
  pageSize,
  totalCount,
  isLoading,
  isSupplierOrderError,
  shouldRenderFilters,
  productsData
}: IProductListMobileProps) {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const router = useRouter();
  const { showModal, hideModal } = useModal();
  const [products, setProducts] = useState<IProductPayLoad[]>([]);
  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const [showBulkSelect, setShowBulkSelect] = useState(false);

  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };
  const hasNextPage = calculateTotalCount({ pageSize, totalCount }) > page;
  const { data: categoryMapperCount } = useGetProductCategoryMapperCountQuery();

  const { filters, setFilters } = useProductFilters();

  const onDeleteProduct = (ids: string[]) => {
    const cloneIds = [...checkedIds];

    showModal({
      body: (
        <ProductDeleteConfirmation
          ids={ids}
          onSuccess={() => {
            setCheckedIds([]);
            setFilters({ page: 1 }, { history: "push" });
          }}
        />
      )
    });
  };

  useEffect(() => {
    if (!products?.length && page > 1) {
      setPage(1);
    }
  }, [products, page]);

  useEffect(() => {
    if (productsData?.length && !isLoading) {
      if (page === 1) {
        setProducts(productsData);
      } else {
        setProducts([...products, ...productsData]);
      }
    } else setProducts([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productsData, isLoading]);

  const onClickProductMenu = (productId?: string) => {
    const foundProduct = products?.find(a => a.id === productId);
    showModal({
      body: (
        <ProductMenu
          title={foundProduct?.title}
          onClickDelete={() => productId && onDeleteProduct([productId])}
          onClickEdit={() => {
            router?.push(`${makePath(routes.product)}/edit/${productId}`);
            hideModal();
          }}
        />
      ),
      modalProps: { showCloseIcon: false }
    });
  };

  const onClickHeaderMenu = () => {
    showModal({
      body: (
        <HeaderMenu
          onClickAddProduct={() => {
            router.push(makePath(routes.createProduct));
            hideModal();
          }}
          onOpenExcelUpload={() =>
            showModal({
              body: () => <ExcelUploadModal />,
              modalProps: {
                showCloseIcon: false
              }
            })
          }
        />
      )
      // modalProps: { showCloseIcon: false }
    });
  };

  const onClickBulkChangePrice = (ids: string[]) => {
    const cloneIds = [...checkedIds];

    showModal({
      width: 450,
      modalProps: {
        showCloseIcon: false
      },
      body: (
        <ProductBulkChangePrice
          ids={ids}
          onSuccess={() => {
            if (cloneIds?.length) setCheckedIds(prev => prev.filter(item => !cloneIds?.find(v => v === item)));
          }}
        />
      )
    });
  };

  const onClickBulkMenu = () => {
    showModal({
      body: (
        <BulkMenu
          onClickBulkDelete={() => {
            onDeleteProduct(checkedIds);
          }}
          onClickBulkPrice={() => {
            onClickBulkChangePrice(checkedIds);
          }}
        />
      ),
      modalProps: { showCloseIcon: false }
    });
  };

  const handleCheckItem = (id: string) => {
    const existId = checkedIds?.find(item => item === id);

    if (existId) {
      setCheckedIds(prev => prev?.filter(item => item !== id));
    } else {
      setCheckedIds(prev => [...prev, id]);
    }
  };

  const handleCheckAllItems = (checked: boolean) => {
    const ids = products
      // ?.filter(item => !checkedIds?.find(v => v === item?.id))
      ?.map(item => item.id) as string[];

    setCheckedIds(checked ? ids : []);
  };

  return (
    <div>
      <Header
        title={t("products")}
        titlePostfix={totalCount ? `(${totalCount})` : undefined}
        searchValue={filters?.title || ""}
        onSearch={e => {
          setFilters({ title: e.target.value }, { history: "push" });
        }}
        onClickMenu={onClickHeaderMenu}
      />

      <div className="flex items-start px-4 justify-between">
        {!showBulkSelect && (
          <div className="mt-4">
            <ProductsFilter
              filtersEndAdornment={
                <>
                  {!!categoryMapperCount?.data?.count && categoryMapperCount?.data?.count > 0 && (
                    <Link href={`${makePath(routes.productCategoryMapper)}`}>
                      <div className="text-[13px] font-medium text-[#00359E] w-fit mr-auto cursor-pointer flex items-center justify-center mt-4">
                        {t("product.syncCategories", { count: categoryMapperCount?.data?.count })}{" "}
                        <Icon icon="solar:alt-arrow-left-outline" className="size-4" />
                      </div>
                    </Link>
                  )}

                  {!!totalCount && !showBulkSelect && (
                    <div
                      className="shrink-0 text-v2-content-on-action-2 py-1.5 border border-transparent text-[13px] font-medium cursor-pointer"
                      onClick={() => setShowBulkSelect(true)}
                    >
                      {t("product.bulk.bulkSelect")}
                    </div>
                  )}
                </>
              }
            />
          </div>
        )}

        {showBulkSelect && (
          <div
            className="flex items-center gap-2 cursor-pointer mt-4"
            onClick={() => handleCheckAllItems(!checkedIds?.length)}
          >
            <CustomCheckbox
              className="p-0 m-0"
              checkboxClassName="p-0"
              indeterminate={!!checkedIds?.length && checkedIds?.length !== totalCount}
              onChange={(e, checked) => handleCheckAllItems(checked)}
              checked={!!checkedIds?.length && checkedIds?.length === totalCount}
              size="small"
            />

            <span className="text-v2-content-tertiary text-[13px] font-medium">
              {t("product.bulk.bulkSelectAll")}{" "}
              {!!checkedIds?.length && `(${t("product.bulk.selected")} ${checkedIds?.length})`}
            </span>
          </div>
        )}
        {showBulkSelect && (
          <div
            className="text-v2-content-on-action-2 text-[13px] font-medium mt-4 shrink-0 cursor-pointer"
            onClick={() => {
              setCheckedIds([]);
              setShowBulkSelect(false);
            }}
          >
            {t("product.bulk.bulkDeselect")}
          </div>
        )}
      </div>
      <div className="px-4 pt-3">
        <div className="">
          {isLoading ? (
            <div className="flex items-center justify-center h-fullMinesHeader">
              <CircularProgress />
            </div>
          ) : !products?.length && !isLoading ? (
            <Box
              width="100%"
              minHeight="50vh"
              display="flex"
              justifyContent="center"
              alignItems="center"
              className="p-4 rounded-lg bg-cards"
            >
              <ProductEmptyList shouldRenderFilters={shouldRenderFilters} />
            </Box>
          ) : isSupplierOrderError ? (
            <Box
              width="100%"
              minHeight="50vh"
              display="flex"
              justifyContent="center"
              alignItems="center"
              className="p-4 rounded-lg bg-cards"
            >
              <Typography>{t("errors.somethingWentWrong")}</Typography>
            </Box>
          ) : (
            <div className="flex flex-col gap-1.5">
              {products?.map(item => (
                <div key={item?.id} className="flex gap-2 items-start">
                  {showBulkSelect && (
                    <CustomCheckbox
                      className="p-0 m-0"
                      checkboxClassName="p-0"
                      size="small"
                      onChange={(e, checked) => handleCheckItem(item?.id)}
                      checked={!!checkedIds?.length && checkedIds?.some(v => v === item?.id)}
                    />
                  )}
                  <SupplierProductCardMobile
                    key={item?.id}
                    id={item?.id}
                    title={item.title}
                    inventory={`${t("product.inventory")}: ${item?.variants?.reduce((intry, variant) => intry + variant.inventory, 0) ?? "0"}`}
                    price={item.cheapestPrice && renderPrice(item.cheapestPrice)}
                    imageUrl={item.cover.url}
                    onClickMenu={id => onClickProductMenu(id)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {products?.length >= pageSize && (
          <ListInfiniteScroll hasNextPage={hasNextPage} fetchNextPage={() => setPage(page + 1)} />
        )}
      </div>

      {!!checkedIds?.length && <StickyActionsFooter onClickBulkActions={onClickBulkMenu} />}
    </div>
  );
}

export default ProductListMobile;
