import AddSupplierStore from "@/components/containers/SupplierStoreSetting/AddSupplierStore";
import Button from "@/components/ui/Button";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Typography } from "@mui/material";
import { IconPlus } from "@tabler/icons-react";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";

interface IProductEmptyListProps {
  shouldRenderFilters: boolean;
}

function ProductEmptyList({ shouldRenderFilters }: IProductEmptyListProps) {
  const makePath = useRoleBasePath();
  const { t } = useTranslation();

  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh_-_310px)] h-full  gap-5">
      <div className="">
        {shouldRenderFilters ? (
          <Image src="/images/product-search-not-found-1.svg" width={196} height={230} alt="empty list palceholder" />
        ) : (
          <Image src="/images/product-list-empty-2.svg" width={196} height={230} alt="empty list palceholder" />
        )}
      </div>

      <div className="flex gap-2 flex-col text-center">
        <Typography variant="h5">{shouldRenderFilters ? t("product.noProducts") : t("product.emptyList")}</Typography>
        <Typography variant="caption">
          {shouldRenderFilters ? t("product.noProductsSubtitle") : t("product.emptyListSubTitle")}
        </Typography>
      </div>
      {!shouldRenderFilters && (
        <div className="flex flex-col gap-3">
          <Link href={makePath(routes.createProduct)}>
            <Button size="lg" variant="secondaryColor">
              <IconPlus />
              {t("product.createProduct")}
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}

export default ProductEmptyList;
