import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import { Close } from "@mui/icons-material";
import { CircularProgress, IconButton } from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { getAgreementContent } from "./supplierContent";
import { useSupplierSignContractMutation } from "@/store/apps/supplier";

interface ISupplierSignContractModalProps {
  address?: string;
  birthCertificateNumber?: string;
  email?: string;
  fullName?: string;
  nationalId?: string;
  phoneNumber?: string;
  postalCode?: string;
  close: () => void;
  onSuccess: () => void;
}

function SupplierSignContractModal({
  close,
  onSuccess,
  address,
  birthCertificateNumber,
  email,
  fullName,
  nationalId,
  phoneNumber,
  postalCode
}: ISupplierSignContractModalProps) {
  const { t } = useTranslation();
  const [checked, setChecked] = useState(false);

  const [mutate, { isLoading, isSuccess }] = useSupplierSignContractMutation();

  const handleClickConfirm = () => {
    mutate().then(() => {
      onSuccess();
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-999 font-medium">{t("supplier.profile.signContractTitle")}</div>
        <IconButton onClick={close} className="block p-0 w-5 h-5">
          <Close className="w-5 h-5" />
        </IconButton>
      </div>
      <div className="max-h-[50vh] overflow-auto">
        <div
          dangerouslySetInnerHTML={{
            __html: getAgreementContent({
              address,
              birthCertificateNumber,
              email,
              fullName,
              nationalId,
              phoneNumber,
              postalCode
            })
          }}
          className="text-gray-999 text-[13px]"
        />
      </div>
      <div>
        <CustomCheckbox
          label={t("supplier.profile.signContractCheckbox")}
          checked={checked}
          onChange={e => setChecked(e.target.checked)}
        />
      </div>

      <div className="flex justify-between items-center">
        <CustomButton color="secondary" onClick={close}>
          {t("supplier.profile.signContractCancelBtn")}
        </CustomButton>
        <CustomButton disabled={!checked || isLoading || isSuccess} onClick={handleClickConfirm}>
          {isLoading && <CircularProgress color="info" size={26} />} {t("supplier.profile.signContractApproveBtn")}
        </CustomButton>
      </div>
    </div>
  );
}

export default SupplierSignContractModal;
