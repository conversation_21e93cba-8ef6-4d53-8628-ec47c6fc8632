import Image from "next/image";
import React from "react";
import { CircularProgress, Typography } from "@mui/material";
import ImportListButton from "../ImportListButton/ImportListButton";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import useCurrency from "@/utils/hooks/useCurrency";
import { TProductCard } from "./types";
import HoverPopover from "../../ui/popover/HoverPopover";
import { Icon } from "@iconify/react";
import ShippingTableContent from "./ShippingTableContent";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { calcProfitAmount } from "../productDetailPage/utils";

function ProductCard({
  className = "",
  productId,
  cover,
  title,
  supplier,
  price,
  retailPrice,
  shippingPrice,
  commission,
  shippingTime,
  shippingPolicies,
  findLocation,
  href,
  imported,
  authenticity,
  isSupplierView
}: TProductCard) {
  const { t } = useTranslation();
  const [{ render: renderPrice, symbol }] = useCurrency();
  const makePath = useRoleBasePath();
  const finalCover = cover?.sizes?.thumbnail || cover?.url;

  const finalPrice = price !== undefined ? renderPrice(price, { showSymbol: false }) : undefined;

  const retailerProfitAmount = calcProfitAmount({ commission: commission || 0, retailPrice: retailPrice || 0 });
  const retailerProfitPercent = commission || 0;

  const LinkComponent = href ? Link : "div";

  const authenticityBadge = {
    HighCopy: {
      bg: "#F2F6FF",
      color: "#00359E",
      title: t("product.highCopy")
    },
    Fake: {
      bg: "#FFEBD8",
      color: "#D26500",
      title: t("product.fake")
    }
  };

  return (
    <LinkComponent href={href!} className={"group relative block" + className}>
      {authenticity && authenticity !== "Original" && (
        <div
          className="rounded px-1.5 py-0.5 text-caption-medium w-fit absolute left-6 top-6 z-10"
          style={{
            background: authenticityBadge?.[authenticity]?.bg,
            color: authenticityBadge?.[authenticity]?.color
          }}
        >
          {authenticityBadge?.[authenticity]?.title}
        </div>
      )}

      {/* -------------------------------------------------------------------------- */
      /*                                 profit box                                 */
      /* -------------------------------------------------------------------------- */}
      {(!!retailerProfitAmount || !!retailerProfitPercent) && (
        <div className="flex items-stretch justify-start absolute right-0 top-9 z-10">
          {retailerProfitPercent && (
            <div className="bg-v2-content-on-action-2 p-1 text-v2-content-on-action-1 flex flex-col items-center justify-center border-l border-v2-content-on-action-disable">
              <div className="text-sm font-bold leading-[10px]">{retailerProfitPercent}</div>
              <div className="text-[8px] leading-[10px] font-medium">{t("retailerProduct.profitPercent")}</div>
            </div>
          )}
          {!!retailerProfitAmount && (
            <div className="bg-v2-surface-action-light rounded-l-lg px-1.5 py-1 flex items-start flex-col justify-center">
              <div className="text-[8px] leading-3 font-medium text-v2-content-tertiary">
                {t("retailerProduct.yourProfitUpTo")}
              </div>
              <div>
                <span className="text-xs leading-4 font-medium text-v2-content-primary">
                  {renderPrice(retailerProfitAmount, { showSymbol: false })}
                </span>
                <span className="text-v2-content-tertiary text-[8px] font-medium leading-3"> {symbol}</span>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="bg-v2-surface-primary group relative w-full h-auto box-border">
        <div className="p-4 flex flex-col gap-2">
          {/* ---------------------------------- Image --------------------------------- */}
          <div className="w-full relative items-center h-[218px] justify-center overflow-hidden flex bg-gray-20 rounded">
            {finalCover?.startsWith("http") && (
              <Image src={finalCover} alt={cover?.alt || title + "_img"} fill className="object-contain" />
            )}
          </div>

          {/* -------------------------------------------------------------------------- */
          /*                                    body                                    */
          /* -------------------------------------------------------------------------- */}
          <div className="flex flex-col gap-1">
            {/* -------------------------------- supplier -------------------------------- */}
            {supplier?.name && supplier?.id && (
              <Link href={supplier?.id ? `${makePath(routes.supplierProducts(supplier.id))}` : ""}>
                <div className="text-v2-content-tertiary text-[10px] leading-4 font-medium ">
                  {t("productItem.by")} <span className="text-v2-content-on-info">{supplier?.name}</span>
                </div>
              </Link>
            )}

            {/* ---------------------------------- title --------------------------------- */}
            <div className="text-[13px] font-medium text-v2-content-primary h-10 line-clamp-2">{title}</div>

            {/* ---------------------------------- price --------------------------------- */}
            {finalPrice !== undefined && (
              <div className="text-left">
                <span className="text-v2-content-primary text-lg font-semibold">{finalPrice} </span>
                <span className="text-xs font-medium text-v2-content-tertiary">{symbol}</span>
              </div>
            )}
          </div>

          {/* -------------------------------------------------------------------------- */
          /*                              footer (on hover)                             */
          /* -------------------------------------------------------------------------- */}

          <hr className="border-v2-border-primary" />

          <div className="pt-1 flex items-center justify-between gap-1">
            {shippingPrice !== undefined && (
              <HoverPopover
                content={<ShippingTableContent shippingPolicies={shippingPolicies} findLocation={findLocation} />}
              >
                <span className="inline-flex items-center text-xs text-v2-content-secondary">
                  {t("productItem.fullDetail")}
                  <Icon icon="solar:alt-arrow-down-line-duotone" width={16} height={16} className="mr-1.5" />
                </span>
              </HoverPopover>
            )}

            {!isSupplierView && productId && (
              <ImportListButton imported={imported} productId={productId}>
                {({ isLoading, onClick, isImported }) => {
                  if (isImported) return null;

                  if (isLoading) {
                    return (
                      <div className="h-fit flex items-center ms-auto">
                        <CircularProgress size={24} />
                      </div>
                    );
                  }

                  return (
                    <div className="h-fit flex items-center ms-auto">
                      <div className="group/add flex items-center gap-1 shrink-0" onClick={onClick}>
                        <Icon icon="ph:plus" className="size-6 shrink-0 text-v2-content-on-info" />
                        <div className="text-sm text-v2-content-on-info max-w-0 overflow-hidden group-hover/add:max-w-64 transition-all duration-300">
                          {t("productItem.add")}
                        </div>
                      </div>
                    </div>
                  );
                }}
              </ImportListButton>
            )}
          </div>
        </div>
      </div>
    </LinkComponent>
  );
}

export default ProductCard;
