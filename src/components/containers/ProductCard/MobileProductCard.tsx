import React from "react";
import { TProductCard } from "./types";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import useCurrency from "@/utils/hooks/useCurrency";
import ImportListButton from "../ImportListButton/ImportListButton";
import { Icon } from "@iconify/react";
import ShippingTableContent from "./ShippingTableContent";
import ClickPopover from "../../ui/popover/ClickPopover";
import Link from "next/link";
import { CircularProgress } from "@mui/material";
import { calcProfitAmount } from "../productDetailPage/utils";

function MobileProductCard(props: TProductCard) {
  const { t } = useTranslation();
  const [{ render: renderPrice, symbol }] = useCurrency();

  const {
    title,
    cover,
    price,
    retailPrice,
    commission,
    productId,
    shippingPrice,
    shippingPolicies,
    shippingTime,
    findLocation,
    href,
    imported,
    isSupplierView
  } = props || {};

  const LinkComponent = href ? Link : "div";
  const finalPrice = price !== undefined ? renderPrice(price, { showSymbol: false }) : undefined;
  const retailerProfitAmount = calcProfitAmount({ commission: commission || 0, retailPrice: retailPrice || 0 });
  const retailerProfitPercent = commission || 0;

  return (
    <LinkComponent
      href={href!}
      className="w-full h-fit max-h-[186px] p-4 rounded-[7px] flex flex-col divide-y divide-v2-border-secondary bg-v2-surface-primary"
    >
      {/* -------------------------------------------------------------------------- */
      /*                                   header                                   */
      /* -------------------------------------------------------------------------- */}

      <div className="flex justify-between gap-1.5 pb-4">
        {/* ---------------------------------- right --------------------------------- */}
        <div className="flex-1 flex flex-col justify-between">
          <div className="text-v2-content-primary text-[13px] leading-5 font-medium line-clamp-3">{title}</div>

          {shippingPrice !== undefined && (
            <ClickPopover
              content={
                <div onClick={e => e.preventDefault()}>
                  <ShippingTableContent shippingPolicies={shippingPolicies} findLocation={findLocation} />
                </div>
              }
            >
              <span
                className="inline-flex items-center text-xs text-v2-content-secondary font-medium"
                onClick={e => e.preventDefault()}
              >
                {t("productItem.fullDetail")}

                <Icon icon="solar:alt-arrow-down-line-duotone" width={16} height={16} className="mr-1.5" />
              </span>
            </ClickPopover>
          )}
        </div>
        {/* ---------------------------------- left ---------------------------------- */}
        <div className="relative items-center h-[81px] w-[81px] shrink-0 justify-center overflow-hidden flex bg-gray-20 rounded-lg">
          {cover?.url?.startsWith("http") && <Image src={cover?.url} alt={cover?.alt || title + "_img"} fill />}
        </div>
      </div>

      {/* -------------------------------------------------------------------------- */
      /*                                   footer                                   */
      /* -------------------------------------------------------------------------- */}
      <div className="flex justify-between pt-4">
        {(!!retailerProfitAmount || !!retailerProfitPercent) && (
          <div className="flex items-center justify-start gap-2">
            {retailerProfitPercent && (
              <div className="bg-v2-surface-info p-1.5 text-v2-content-on-info flex flex-col items-center justify-center rounded-md">
                <div className="text-sm font-bold leading-[10px]">{retailerProfitPercent}</div>
                <div className="text-[8px] leading-[6px] font-semibold">{t("retailerProduct.profitPercent")}</div>
              </div>
            )}
            {!!retailerProfitAmount && (
              <div className="px-1.5 py-1 flex items-start flex-col justify-center">
                <div className="text-[10px] leading-4 font-normal text-v2-content-tertiary">
                  {t("retailerProduct.yourProfitUpTo")}
                </div>

                <div className="-mt-1">
                  <span className="text-xs leading-4 font-medium text-v2-content-on-info">
                    {renderPrice(retailerProfitAmount, { showSymbol: false })}
                  </span>

                  <span className="text-v2-content-tertiary text-[10px] font-normal leading-4"> {symbol}</span>
                </div>
              </div>
            )}
          </div>
        )}

        <div className="flex gap-3 items-center ms-auto">
          {/* ---------------------------------- price --------------------------------- */}
          {finalPrice !== undefined && (
            <div className="">
              <span className="text-v2-content-primary text-[15px] leading-6 font-medium">{finalPrice}</span>

              <span className="text-v2-content-tertiary text-xs font-medium leading-4"> {symbol}</span>
            </div>
          )}

          {productId && !isSupplierView && (
            <ImportListButton imported={imported} productId={productId}>
              {({ isLoading, onClick, isImported }) => {
                if (isImported) return null;

                if (isLoading) {
                  return (
                    <div className="h-fit flex items-center ms-auto">
                      <CircularProgress size={24} />
                    </div>
                  );
                }

                return (
                  <div className="h-fit flex items-center ms-auto">
                    <div className="w-px h-5 bg-v2-border-primary me-3" />

                    <div className="" onClick={onClick}>
                      <Icon icon="ph:plus" className="size-6 shrink-0 text-v2-content-primary" />
                    </div>
                  </div>
                );
              }}
            </ImportListButton>
          )}
        </div>
      </div>
    </LinkComponent>
  );
}

export default MobileProductCard;
