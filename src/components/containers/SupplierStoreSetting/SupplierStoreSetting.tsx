import SelectedStore from "./SelectedStore";
import { useGetSupplierStoreQuery } from "@/store/apps/supplier";
import StoreStepper from "./StoreStepper/StoreStepper";
import { CircularProgress } from "@mui/material";
import { useState } from "react";

function SupplierStoreSetting() {
  const { data: supplierStore, isLoading: isStoreSingleLoading } = useGetSupplierStoreQuery();
  const isEdit = supplierStore?.data?.id;
  const isActive = supplierStore?.data?.isActive;
  const [inProgress, setInProgress] = useState(false);

  if (isStoreSingleLoading) {
    return (
      <div className="flex items-center justify-center bg-cards h-screen">
        <CircularProgress />
      </div>
    );
  }

  if (isEdit && !inProgress) {
    return <SelectedStore isStoreSingleLoading={isStoreSingleLoading} supplierStore={supplierStore} />;
  }

  return (
    <StoreStepper
      isStoreSingleLoading={isStoreSingleLoading}
      supplierStore={supplierStore}
      setInProgress={setInProgress}
    />
  );
}

export default SupplierStoreSetting;
