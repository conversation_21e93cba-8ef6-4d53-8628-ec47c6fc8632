import { routes } from "@/constants/routes";
import { TSupplierIntegrationResponse } from "@/store/apps/supplier/types";
import useLanguage from "@/utils/hooks/useLanguage";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import { usePathname, useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import Action from "./Action";
import { useCheckValidStep } from "./hooks/useCheckValidStep";
import Link from "next/link";
import { ensureUrlScheme } from "@/utils/helpers";
import MobileAppBar from "../../mobileAppBar/MobileAppBar";
import ProfileContentContainer from "../../ProfileStepper/ProfileContentContainer";

interface IViewProps {
  supplierStore?: TSupplierIntegrationResponse;
  isStoreSingleLoading?: boolean;
  onBack: () => void;
  onContinue: () => void;
  hasWebsiteType?: boolean;
  hasWebsiteLink?: boolean;
}

function View({
  isStoreSingleLoading,
  supplierStore,
  onBack,
  onContinue,
  hasWebsiteType = true,
  hasWebsiteLink = true
}: IViewProps) {
  const { t } = useTranslation();
  const [currentLang] = useLanguage();
  const pathname = usePathname();
  const router = useRouter();
  const { hideModal } = useModal();
  const makePath = useRoleBasePath();

  // useCheckValidStep({ isStoreSingleLoading, supplierStore });

  if (isStoreSingleLoading) {
    return (
      <div className="flex items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  if (!supplierStore?.data?.id) {
    return null;
  }

  return (
    <>
      <MobileAppBar title={t("connectStore")} hasBack onBack={onBack} />
      <ProfileContentContainer containerClassName="!py-4">
        <div>
          <div className="xmd:flex hidden items-center w-fit gap-2 mb-4 cursor-pointer" onClick={onBack}>
            <Icon icon="solar:arrow-right-outline" className="size-5 text-v2-content-tertiary " />

            <p className="text-center text-v2-content-tertiary text-body2-medium  font-semibold flex-1">{t("back")}</p>
          </div>

          <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4  relative">
            <div className="xmd:p-6 p-0 pb-4 border-b border-b-v2-border-primary flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Icon icon="solar:folder-path-connect-outline" className="size-5" />
                <span className="text-body1-medium text-v2-content-primary !font-semibold">
                  {t("store.productStore.title")}
                </span>
              </div>

              <span className="text-body3-medium text-v2-content-tertiary">{t("store.productStore.subtitle")}</span>
            </div>

            <div className="xmd:p-6 p-0 pt-6 pb-16">
              <div className="flex flex-col ">
                <div className="flex flex-col items-center gap-6">
                  <Icon icon="solar:confetti-bold-duotone" className="size-14 text-v2-content-on-success-2" />
                  <h2 className="text-gray-999 text-body2-bold">{t("store.view.title")}</h2>
                </div>
                <div className="mt-6 border border-solid border-v2-border-primary p-4 flex flex-col gap-6 rounded-lg xmd:w-[70%] w-full mx-auto">
                  {hasWebsiteType && (
                    <div className="flex  justify-between items-center border-b border-b-v2-border-primary pb-4">
                      <span className="text-body4-medium text-v2-content-tertiary">{t("store.view.websiteType")}</span>
                      <div className="flex items-center gap-2">
                        {!!supplierStore?.data?.logo && (
                          <div
                            dangerouslySetInnerHTML={{ __html: supplierStore?.data?.logo }}
                            className="!size-5 [&>svg]:!size-5 "
                          />
                        )}
                        <span className="text-body4-medium text-gray-999">
                          {supplierStore?.data?.platform?.name?.find(l => l.iso === currentLang?.value)?.text}
                        </span>
                      </div>
                    </div>
                  )}

                  {hasWebsiteLink && (
                    <div className="flex justify-between items-center border-b border-b-v2-border-primary pb-4">
                      <span className="text-body4-medium text-v2-content-tertiary">{t("store.view.websiteLink")}</span>

                      <Link
                        target="_blank"
                        rel="noopener noreferrer"
                        href={ensureUrlScheme(supplierStore?.data?.url ?? "")}
                      >
                        <span className="text-body4-medium text-gray-999">{supplierStore?.data?.url}</span>
                      </Link>
                    </div>
                  )}

                  <div className="flex justify-between items-center border-b border-b-v2-border-primary pb-4">
                    <span className="text-body4-medium text-v2-content-tertiary">{t("store.view.syncProduct")}</span>
                    <div
                      className={twMerge(
                        "flex items-center gap-1 px-2 py-1 rounded",
                        supplierStore?.data?.preferences?.syncProducts
                          ? "bg-success-50 text-success-500"
                          : "bg-gray-50 text-gray-500"
                      )}
                    >
                      <Icon
                        icon={supplierStore?.data?.preferences?.syncProducts ? "charm:tick" : "mingcute:close-line"}
                        className="size-3.5"
                      />
                      <span className="text-body4-medium">
                        {supplierStore?.data?.preferences?.syncProducts ? t("store.active") : t("store.deactive")}
                      </span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-body4-medium text-v2-content-tertiary">{t("store.view.syncOrders")}</span>
                    <div
                      className={twMerge(
                        "flex items-center gap-1 px-2 py-1 rounded",
                        supplierStore?.data?.preferences?.syncOrders
                          ? "bg-success-50 text-success-500"
                          : "bg-gray-50 text-gray-500"
                      )}
                    >
                      <Icon
                        icon={supplierStore?.data?.preferences?.syncOrders ? "charm:tick" : "mingcute:close-line"}
                        className="size-3.5"
                      />
                      <span className="text-body4-medium">
                        {supplierStore?.data?.preferences?.syncOrders ? t("store.active") : t("store.deactive")}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Action continueText={t("confirm&close")} onBack={onBack} onContinue={onContinue} />
      </ProfileContentContainer>
    </>
  );
}

export default View;
