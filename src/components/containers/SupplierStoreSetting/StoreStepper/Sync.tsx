import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { TSupplierIntegrationResponse } from "@/store/apps/supplier/types";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import MobileAppBar from "../../mobileAppBar/MobileAppBar";
import ProfileContentContainer from "../../ProfileStepper/ProfileContentContainer";
import Action from "./Action";
import { useCheckValidStep } from "./hooks/useCheckValidStep";

interface IWebsiteProps {
  supplierStore?: TSupplierIntegrationResponse;
  isStoreSingleLoading?: boolean;
  isConnecting?: boolean;
  onContinue: () => void;
  onBack: () => void;
}

function Sync({ isStoreSingleLoading, supplierStore, isConnecting, onContinue, onBack }: IWebsiteProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();

  useCheckValidStep({ isStoreSingleLoading, supplierStore });

  const getStatus = () => {
    if (!supplierStore?.data?.id) return "";

    if (isConnecting) {
      return "loading";
    }
    if (supplierStore?.data?.isConnected) {
      return "connected";
    }
    if (!supplierStore?.data?.isConnected) {
      return "failed";
    }
  };
  const status = getStatus();

  const statusRender = {
    connected: (
      <>
        <div className="flex flex-col gap-2 items-center justify-center mb-6 mt-20">
          <Icon icon="ph:seal-check-fill" className="size-11 text-success-500" />
          <p className="text-success-500 text-body3-medium">{t("store.progressbar.successTitle")}</p>
          <p className="text-gray-999 text-caption-regular font-medium">{t("store.progressbar.successSubtitle")}</p>
        </div>

        <div className="bg-v2-surface-info flex items-center gap-1.5 p-2.5 rounded-md mt-32">
          <Icon icon="solar:info-circle-outline" className="size-5 shrink-0" />
          <span className="text-body4-medium text-gray-999">{t("store.alertSyncLoading")}</span>
        </div>
      </>
    ),
    failed: (
      <>
        <div className="flex flex-col gap-2 items-center mb-6 mt-20">
          <Icon icon="solar:danger-triangle-linear" className="size-11 text-error-500" />
          <p className="text-error-500 text-body3-medium">{t("store.progressbar.loadingTitle")}</p>
          <p className="text-gray-999 text-caption-regular font-medium">{t("store.progressbar.loadingSubtitle")}</p>
        </div>
      </>
    ),
    loading: (
      <>
        <div className="flex flex-col gap-2 items-center justify-center mb-6 mt-20">
          <Icon icon="solar:history-3-outline" className="size-11 text-warning-500" />
          <p className="text-warning-500 text-body3-medium">{t("store.progressbar.loadingTitle")}</p>
          <p className="text-gray-999 text-caption-regular font-medium">{t("store.progressbar.loadingSubtitle")}</p>
        </div>

        <div className="bg-v2-surface-info flex items-center gap-1.5 p-2.5 rounded-md mt-32">
          <Icon icon="solar:info-circle-outline" className="size-5 shrink-0" />
          <span className="text-body4-medium text-gray-999">{t("store.alertSyncLoading")}</span>
        </div>
      </>
    )
  };

  if (isStoreSingleLoading) {
    return (
      <div className="flex items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  return (
    <div>
      <MobileAppBar title={t("connectStore")} hasBack onBack={onBack} />
      <ProfileContentContainer containerClassName="!py-4">
        <div className="xmd:flex hidden items-center w-fit gap-2 mb-4 cursor-pointer" onClick={onBack}>
          <Icon icon="solar:arrow-right-outline" className="size-5 text-v2-content-tertiary " />

          <p className="text-center text-v2-content-tertiary text-body2-medium  font-semibold flex-1">{t("back")}</p>
        </div>

        <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4  relative">
          <div className="xmd:p-6 p-0 pb-4 border-b border-b-v2-border-primary flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <Icon icon="solar:folder-path-connect-outline" className="size-5" />
              <span className="text-body1-medium text-v2-content-primary !font-semibold">
                {t("store.productStore.title")}
              </span>
            </div>

            <span className="text-body3-medium text-v2-content-tertiary">{t("store.productStore.subtitle")}</span>
          </div>

          <div className="xmd:p-6 p-0">{status && statusRender[status]}</div>
        </div>
        <div className="xmd:mt-4 mt-0">
          <Action onContinue={onContinue} onBack={onBack} disabledContinue={status !== "connected"} />
        </div>
      </ProfileContentContainer>
    </div>
  );
}

export default Sync;
