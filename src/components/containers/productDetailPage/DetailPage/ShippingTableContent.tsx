import React from "react";
import { useTranslation } from "react-i18next";
import useCurrency from "@/utils/hooks/useCurrency";
import { TSupplierShippingData } from "@/store/apps/supplier/types";
import { TMetaLocations } from "@/store/apps/meta/types";
import { getShippingTypeItems } from "@/components/forms/SupplierShippingForm/utils";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";

function ShippingTableContent({
  shippingPolicies,
  findLocation
}: {
  shippingPolicies?: Pick<
    TSupplierShippingData,
    "shippingTime" | "shippingTo" | "rate" | "shippingCarrier" | "rateType" | "extraItemRate" | "excluded"
  >[];
  findLocation: (cityId: string | number) => TMetaLocations | null;
}) {
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  const shippingTypeItems = getShippingTypeItems({ t });
  const prepaidValue = (row: TSupplierShippingData) =>
    shippingTypeItems?.find(item => item?.id === row?.prepaid)?.shortLabel;

  const shippingPoliciesData = shippingPolicies?.filter(a => !a.excluded);
  const shippingPoliciesExcludedCities =
    shippingPolicies?.filter(a => a.shippingTo && a.excluded)?.map(item => findLocation(item.shippingTo!)?.name) || [];

  return (
    <div className="flex flex-col w-full gap-2">
      <TableContainer className="bg-transparent">
        <Table className="overflow-hidden">
          <TableHead>
            <TableRow>
              <TableCell align="right" className="text-right text-[13px] text-gray-400 py-[9px]">
                {t("productItem.shippingTo")}
              </TableCell>
              <TableCell align="right" className="text-right text-[13px] text-gray-400 py-[9px]">
                {t("productItem.shippingCarrier")}
              </TableCell>
              <TableCell align="right" className="text-right text-[13px] text-gray-400 py-[9px]">
                {t("productItem.shippingTime")}
              </TableCell>
              <TableCell align="right" className="text-right text-[13px] text-gray-400 py-[9px]">
                {t("productItem.shippingRate")}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {shippingPoliciesData?.map(row => (
              <TableRow key={row.shippingTo} className="h-[37.5px]">
                <TableCell align="right" className="text-right text-[13px] text-gray-999 !py-2">
                  {row.shippingTo === "00000000-0000-0000-0000-000000000000"
                    ? t("productItem.allCities")
                    : row.shippingTo
                      ? findLocation(row.shippingTo)?.name
                      : "-"}
                </TableCell>
                <TableCell align="right" className="text-right text-[13px] text-gray-999 !py-2">
                  <span className="ms-1 px-1.5 py-px rounded-full bg-v2-surface-action-light text-xs text-v2-content-on-info">
                    {row?.shippingCarrier?.name || ""}
                  </span>
                </TableCell>
                <TableCell align="right" className="text-right text-[13px] text-gray-999 !py-2">
                  {row?.shippingTime?.min ? row?.shippingTime?.min : ""} {t("productItem.shippingTimeTo")}{" "}
                  {row?.shippingTime?.max ? row?.shippingTime?.max : ""} {t("productItem.shippingTimeDays")}
                </TableCell>
                <TableCell align="right" className="text-right text-[13px] text-gray-999 !py-2">
                  {renderPrice(Number(row.rate))}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {!!shippingPoliciesExcludedCities?.length && (
        <div className="p-1.5 rounded bg-v2-surface-info text-xs text-v2-content-on-info font-medium">
          <span className="">{t("productItem.dontHaveShippingTo")}</span>{" "}
          {shippingPoliciesExcludedCities?.map((item, index) => (
            <span key={index}>
              {item}
              {index < shippingPoliciesExcludedCities?.length - 1 ? "،" : ""}{" "}
            </span>
          ))}
        </div>
      )}
    </div>
  );
}

export default ShippingTableContent;
