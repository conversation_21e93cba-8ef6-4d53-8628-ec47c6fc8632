import Collapse from "@/components/ui/Collapse/Collapse";
import { STATUS_FAILED, STATUS_LOADING, STATUS_SUCCESS } from "@/constants/product";
import { routes } from "@/constants/routes";
import { usePostProductMutation, usePutProductMutation } from "@/store/apps/product";
import { TProductBody, TProductForm } from "@/store/apps/product/types";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { yupResolver } from "@hookform/resolvers/yup";
import { Icon } from "@iconify/react";
import { Box, CircularProgress, Stack } from "@mui/material";
import { useParams, useRouter } from "next/navigation";
import React, { useRef, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import ProductAdditionalForm from "../ProductAdditional/ProductAdditionalForm";
import { IProductAdditionalFormRefProps } from "../ProductAdditional/types";
import { ProductFormValidation } from "../validation";
import General from "./sections/general";
import Pricing from "./sections/pricing";
import { THasVarintsStaus, TOnStateChange } from "./types";
import Button from "@/components/ui/Button";

const ProductForm = ({
  onStateChange,
  initialValues,
  initialHasVariants
}: {
  onStateChange: TOnStateChange;
  initialValues?: TProductForm;
  initialHasVariants?: boolean;
}) => {
  const router = useRouter();
  const params = useParams();
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal, hideModal } = useModal();
  const productAdditionalFormRef = useRef<IProductAdditionalFormRefProps>(null);
  const productId = params?.id as string;
  const isEdit = !!productId;
  const [postProduct, { isLoading: isPostProductLoading }] = usePostProductMutation();
  const [putProduct, { isLoading: isPutProductLoading }] = usePutProductMutation();
  const isLoadingSubmitting = isPostProductLoading || isPutProductLoading;
  const [hasVariants, setHasVariants] = useState<THasVarintsStaus>(initialHasVariants ? "yes" : "no");

  const formMethods = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(ProductFormValidation({ hasVariants: hasVariants === "yes", isEdit }) as any) as any
    // mode: "onBlur"
  });
  const {
    handleSubmit,
    setError,
    reset,
    watch,
    formState: { errors }
  } = formMethods;

  console.log("errors", errors);

  const onSubmit = async (values: TProductForm) => {
    onStateChange(STATUS_LOADING);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const {
      variants = [],
      sku,
      authenticity,
      backorder,
      commission,
      condition,
      is_active,
      map,
      images,
      status,
      retail_price,
      inventory,
      ...rest
    } = values;
    const productAsVariant = {
      sku,
      retail_price: retail_price ? retail_price?.toString() : undefined,
      inventory,
      id: initialValues?.variants?.[0]?.id as string,
      authenticity,
      backorder,
      commission: commission ? commission?.toString() : undefined,
      condition,
      is_active,
      map: map ? map?.toString() : undefined,
      options: {}
    };

    const modifiedVariants: any = variants?.map(variantItem => ({
      ...variantItem,
      retail_price: variantItem?.retail_price ? variantItem?.retail_price?.toString() : variantItem?.retail_price,
      commission: variantItem?.commission ? variantItem?.commission?.toString() : variantItem?.commission,
      map: variantItem?.map ? variantItem?.map?.toString() : variantItem?.map
    }));

    const totalVariants = hasVariants !== "yes" ? [productAsVariant] : modifiedVariants;
    const body = {
      ...rest,
      variants: totalVariants as TProductBody["variants"],
      status,
      images: images?.map(item => ({ url: item?.url, marked_as_cover: item?.markedAsCover })) as TProductBody["images"]
    };

    const errorSetter = (field: string, message: string) => {
      if (field.startsWith("variants.0.")) setError(field.replace("variants.0.", "") as any, { message });
      else setError(field as any, { message });
    };

    try {
      const res = isEdit ? await putProduct({ body, id: productId }) : await postProduct({ body });
      if ("error" in res && res.error.data) {
        onStateChange(STATUS_FAILED);
        clientDefaultErrorHandler({
          error: (res as any)?.error,
          bodyError: res.error.data,
          setFieldError: errorSetter
        });
      } else if ("data" in res && res.data.status === "OK") {
        if (isEdit) {
          // on edit
          onStateChange(STATUS_SUCCESS);
        } else {
          // on add new
          productAdditionalFormRef?.current?.handleSubmitData?.(res.data.data?.id);
          reset();
          setHasVariants("no");
          onStateChange(STATUS_SUCCESS);
        }
      } else {
        onStateChange(STATUS_FAILED);
      }
    } catch (err: any) {
      onStateChange(STATUS_FAILED);
    }
  };

  const handleCancel = () => {
    showModal({
      title: t("product.cancelModalTitle"),
      subTitle: t("product.cancelModalTitleDesc"),
      icon: "/images/svgs/danger2.svg",
      actions: [
        {
          label: t("product.cancel"),
          onClick: () => hideModal(),
          variant: "secondaryGray"
        },
        {
          label: t("product.cancelModalYes"),
          variant: "warningPrimary",
          onClick: () => {
            hideModal();
            const hasHistory = typeof window !== "undefined" && window.history.length > 1;

            if (hasHistory) {
              router.back();
            } else {
              router.push(makePath(routes.product));
            }
          }
        }
      ]
    });
  };

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col gap-4">
          <Collapse
            title={t("productForm.steps.productDetails")}
            icon={<Icon icon="solar:widget-2-outline" className="size-6 text-gray-999" />}
            initialIsOpen
          >
            <General />
          </Collapse>

          <Collapse
            title={t("productForm.pricing")}
            icon={<Icon icon="solar:dollar-minimalistic-outline" className="size-6 text-gray-999" />}
            initialIsOpen
          >
            <Pricing {...{ setHasVariants, hasVariants, isEdit }} />
          </Collapse>
          <Collapse
            title={t("productForm.specialConditions")}
            subTitle={t("productForm.specialConditionsSubtitle")}
            icon={<Icon icon="solar:delivery-outline" className="size-6 text-gray-999" />}
            initialIsOpen
          >
            <ProductAdditionalForm ref={productAdditionalFormRef} productId={productId} />
          </Collapse>

          <Stack mt={4} mb={2} gap={1} direction="row">
            <Stack flexGrow={1}>
              <Box>
                <Button color="inherit" type="button" size="xl" variant="secondaryGray" onClick={handleCancel}>
                  {t("product.alert.cancel")}
                </Button>
              </Box>
            </Stack>
            <Stack direction="row" gap={1}>
              {/* {<Button size="large" variant="outlined" color="inherit" >
                {t('productItem.actionBar.save')}
              </Button>} */}
              <Button variant="primary" type="submit" size="xl" fullWidth disabled={isLoadingSubmitting}>
                {isLoadingSubmitting && <CircularProgress color="info" size={20} className="ml-2" />}{" "}
                {t("productForm.publish")}
              </Button>
            </Stack>
          </Stack>
        </div>
      </form>
    </FormProvider>
  );
};

export default React.memo(ProductForm);
