import SelectCategoryWithModal from "@/components/containers/SelectCategoryModal/SelectCategoryWithModal";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import { CustomTags } from "@/components/ui/CustomTags";
import Editor from "@/components/ui/Editor/Editor";
import Input from "@/components/ui/inputs/Input";
import InputLabel from "@/components/ui/inputs/Input/InputLabel";
import { VARIANT_STATUS_ACTIVE, VARIANT_STATUS_INACTIVE } from "@/constants/product";
import { TProductForm } from "@/store/apps/product/types";
import { Controller, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import ProductImages from "../../ProductImages";

function General() {
  const { t } = useTranslation();
  const {
    control,
    setError,
    formState: { errors, defaultValues }
  } = useFormContext<TProductForm>();

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-4">
        <div>
          <Controller
            name="title"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                autoComplete="off"
                label={t("product.title")}
                placeholder={t("product.title")}
                error={Boolean(error?.message)}
                helperText={error?.message || t("product.titleHint")}
                labelEndAdornment={<div className="text-xs">{t("product.titleChar", { count: 120 })}</div>}
              />
            )}
          />
        </div>

        <div>
          <Controller
            name="description"
            control={control}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <>
                <InputLabel htmlFor="description" containerClassName="mb-1">
                  {t("product.description")}
                </InputLabel>
                <Editor
                  initialValue={value}
                  onChange={value => onChange(value)}
                  placeholder={t("product.description")}
                  error={Boolean(error?.message)}
                  helperText={error?.message || t("product.descriptionHint")}
                />
              </>
            )}
          />
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <div className="font-medium text-base text-v2-content-primary">{t("productForm.productGalleryAndVideo")}</div>

        <Controller
          name="images"
          control={control}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <div>
              <ProductImages
                images={value ? value : []}
                setImages={v => {
                  Array.isArray(v) && onChange(v);
                }}
                error={error?.message}
                onError={msg => {
                  setTimeout(() => {
                    setError("images", { message: t(`${msg}`) });
                  }, 0);
                }}
              />
              {error?.message && <InputHelper className="mt-1">{error?.message}</InputHelper>}
            </div>
          )}
        />
      </div>

      <div className="flex flex-col gap-4">
        <div className="font-medium text-base text-v2-content-primary">{t("product.category")}</div>

        <div>
          <Controller
            name="category_id"
            control={control}
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <>
                <InputLabel containerClassName="mb-1">{t("product.productCategory")}</InputLabel>
                <SelectCategoryWithModal
                  initialValue={value ? value : undefined}
                  placeholder={t("product.categoryPlaceholder")}
                  onChange={categoryId => {
                    onChange(categoryId);
                  }}
                  error={Boolean(error?.message)}
                />
                {!!error?.message && <InputHelper className="mt-1">{error?.message}</InputHelper>}
              </>
            )}
          />
        </div>

        <div>
          <Controller
            name="tags"
            control={control}
            render={({ field: { name, value, onChange, onBlur }, fieldState: { error } }) => (
              <>
                <InputLabel htmlFor="tags" optional requiredStar={false} containerClassName="mb-1">
                  {t("product.tags")}
                </InputLabel>
                <CustomTags
                  optional
                  requiredStar={false}
                  handleBlur={onBlur}
                  value={value ?? []}
                  onChange={({ tagValues }) => onChange(tagValues)}
                  name={name}
                  error={Boolean(error?.message)}
                  helperText={error?.message ? error?.message : t("product.tagHint")}
                  placeholder={t("product.tagsPlaceholder")}
                />
                {!!error?.message && <InputHelper className="mt-1">{error?.message}</InputHelper>}
              </>
            )}
          />
          <div className="text-xs text-v2-content-tertiary mt-1">{t("product.tagHint")}</div>
        </div>
      </div>

      <div className="md:-mt-1">
        <Controller
          name="status"
          control={control}
          render={({ field: { name, value, onChange, onBlur }, fieldState: { error } }) => (
            <>
              <InputLabel htmlFor="status" containerClassName="mb-1">
                {t("product.status")}
              </InputLabel>
              <div className="-mt-0.5 mb-px">
                <CustomRadio
                  label={t("product.statusItems.Active")}
                  checked={value === VARIANT_STATUS_ACTIVE}
                  value={VARIANT_STATUS_ACTIVE}
                  name="status"
                  onChange={e => {
                    onChange(e.target.value);
                  }}
                />
                <CustomRadio
                  checked={value === VARIANT_STATUS_INACTIVE}
                  onChange={e => {
                    onChange(e.target.value);
                  }}
                  name="status"
                  label={t("product.statusItems.Inactive")}
                  value={VARIANT_STATUS_INACTIVE}
                />
              </div>
              {!!error?.message && <InputHelper>{error?.message}</InputHelper>}
            </>
          )}
        />
      </div>
    </div>
  );
}

export default General;
