import { TProductStatuses } from "@/store/apps/product/types";
import i18n from "@/utils/i18n";
import * as yup from "yup";

export const ProductFormValidation = ({ hasVariants, isEdit }: { hasVariants: boolean; isEdit?: boolean }) =>
  new yup.ObjectSchema({
    // variants: yup.array(),
    // has_special_condition: yup.boolean(),
    variants: yup.array().when([], schema => {
      if (hasVariants) {
        return yup
          .array(
            yup.object({
              sku: yup
                .string()
                .typeError(i18n.t("product.validations.required"))
                .nullable()
                .transform((o, c) => (o === "" ? null : c))
                .min(3, i18n.t("product.validations.min3")),
              // .when([], schema => {
              //   if (isEdit) {
              //     return yup
              //       .string()
              //       .typeError(i18n.t("product.validations.required"))
              //       .required(i18n.t("product.validations.required"))
              //       .min(3, i18n.t("product.validations.min3"));
              //   }
              //   return schema;
              // }),
              retail_price: yup
                .number()
                .typeError(i18n.t("product.validations.required"))
                .required(i18n.t("product.validations.required"))
                .min(1, i18n.t("supplier.profile.validations.positive")),
              // .test("price", i18n.t("product.validations.retailPrice"), (value, context) => {
              //   if (value === undefined) return false;
              //   const { minimum_retail_price, listing_price } = context.parent;
              //   return value >= listing_price && value >= minimum_retail_price;
              // }),
              map: yup
                .number()
                .nullable()
                // .typeError(i18n.t("product.validations.required"))
                .transform((value, originalValue) => (originalValue === "" ? undefined : value))
                .test("map", i18n.t("product.validations.map"), (value, context) => {
                  if (value == null) return true;
                  const { commission } = context.parent;
                  return value <= commission;
                }),
              is_active: yup.boolean(),
              authenticity: yup
                .string()
                .typeError(i18n.t("product.validations.required"))
                .required(i18n.t("product.validations.required")),
              condition: yup
                .string()
                .typeError(i18n.t("product.validations.required"))
                .required(i18n.t("product.validations.required")),
              backorder: yup.boolean(),
              commission: yup
                .number()
                .typeError(i18n.t("product.validations.required"))
                .required(i18n.t("product.validations.required"))
                .min(1, i18n.t("supplier.profile.validations.positive"))
                .max(100, i18n.t("product.validations.max100")),

              // minimum_retail_price: yup
              //   .number()
              //   .typeError(i18n.t("product.validations.required"))
              //   .required(i18n.t("product.validations.required"))
              //   .test("price", i18n.t("product.validations.minimumRetailPrice"), (value, context) => {
              //     if (value === undefined) return false;
              //     const { listing_price } = context.parent;
              //     return value >= listing_price;
              //   }),
              // listing_price: yup
              //   .number()
              //   .typeError(i18n.t("product.validations.required"))
              //   .required(i18n.t("product.validations.required"))
              //   .min(1, i18n.t("supplier.profile.validations.positive")),
              inventory: yup
                .number()
                .typeError(i18n.t("product.validations.required"))
                .required(i18n.t("product.validations.required")),
              options: yup
                .object()
                .typeError(i18n.t("product.validations.required"))
                .required(i18n.t("product.validations.required"))
            })
          )
          .min(1, i18n.t("product.validations.variantsNotCompleted"));
      }
      return schema;
    }),
    sku: yup.string().when([], schema => {
      if (!hasVariants) {
        // if (isEdit) {
        //   return yup
        //     .string()
        //     .required(i18n.t("product.validations.required"))
        //     .min(3, i18n.t("product.validations.min3"));
        // }

        return yup.string().nullable();
        // .required(i18n.t("product.validations.required"))
        // .min(3, i18n.t("product.validations.min3"))
      }
      return schema;
    }),
    title: yup
      .string()
      .required(i18n.t("product.validations.required"))
      .min(5, i18n.t("product.validations.minLengthTitle"))
      .max(120, i18n.t("product.validations.maxChar", { amount: 120 })),
    category_id: yup.string().required(i18n.t("product.validations.required")),
    description: yup.string().required(i18n.t("product.validations.required")),
    images: yup
      .array()
      .of(
        yup.object().shape({
          url: yup.string(),
          markedAsCover: yup.boolean()
        })
      )
      .test("at-least-one-cover", i18n.t("product.validations.coverIsRequired"), (images: any) => {
        return images && images.some((image: any) => image.markedAsCover === true);
      }),
    inventory: yup
      .number()
      .typeError(i18n.t("product.validations.required"))
      .when([], schema => {
        if (!hasVariants) {
          return yup
            .number()
            .typeError(i18n.t("product.validations.required"))
            .required(i18n.t("product.validations.required"));
        }
        return schema;
      }),
    retail_price: yup.number().when([], schema => {
      if (!hasVariants) {
        return yup
          .number()
          .typeError(i18n.t("product.validations.required"))
          .required(i18n.t("product.validations.required"));
        // .test("price", i18n.t("product.validations.retailPrice"), (value, context) => {
        //   if (value === undefined) return false;
        //   const { minimum_retail_price, listing_price } = context.parent;
        //   return value >= listing_price && value >= minimum_retail_price;
        // });
      }
      return schema;
    }),
    commission: yup.number().when([], schema => {
      if (!hasVariants) {
        return yup
          .number()
          .typeError(i18n.t("product.validations.required"))
          .required(i18n.t("product.validations.required"))
          .min(1, i18n.t("supplier.profile.validations.positive"))
          .max(100, i18n.t("product.validations.max100"));
      }
      return schema;
    }),
    map: yup
      .number()
      .nullable()
      .transform((value, originalValue) => (originalValue === "" ? undefined : value))
      .when([], schema => {
        if (!hasVariants) {
          return yup
            .number()
            .transform((value, originalValue) => (originalValue === "" ? undefined : value))
            .nullable()
            .test("map", i18n.t("product.validations.map"), function (value) {
              const { commission } = this.parent;
              if (value == null) return true;
              return value <= commission;
            });
        }
        return schema;
      }),
    is_active: yup.boolean(),
    authenticity: yup
      .string()
      .typeError(i18n.t("product.validations.required"))
      .when("hasVariants", {
        is: true,
        then: schema => schema.required(i18n.t("product.validations.required")),
        otherwise: schema => schema.notRequired().nullable()
      }),
    condition: yup
      .string()
      .typeError(i18n.t("product.validations.required"))
      .when("hasVariants", {
        is: true,
        then: schema => schema.required(i18n.t("product.validations.required")),
        otherwise: schema => schema.notRequired().nullable()
      }),

    backorder: yup.boolean(),

    status: yup
      .string()
      .oneOf(["Active", "Inactive", "InReview", "Rejected"] as TProductStatuses[])
      .required(i18n.t("product.validations.required")),
    tags: yup.array().of(yup.string())
    // return_policy: yup.object().test("return", i18n.t("product.specialPolicy"), (value, context) => {
    //   const { shipping_policies, has_special_condition } = context.parent;
    //   return has_special_condition ? Boolean(value || shipping_policies) : true;
    // }),
    // shipping_policies: yup.array().test("shipping", i18n.t("product.specialPolicy"), (value, context) => {
    //   const { return_policy, has_special_condition } = context.parent;
    //   return has_special_condition ? Boolean(return_policy || value) : true;
    // })
  });
