/* eslint-disable @typescript-eslint/no-unused-vars */

import Collapse2 from "@/components/ui/Collapse2/Collapse2";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import Input from "@/components/ui/inputs/Input";
import NumberInput from "@/components/ui/inputs/NumberInput";
import PriceInput from "@/components/ui/inputs/PriceInput";
import { VARIANT_STATUS_ACTIVE, VARIANT_STATUS_INACTIVE } from "@/constants/product";
import { TProductForm } from "@/store/apps/product/types";
import { omitEmptyValues, toCommas } from "@/utils/helpers";
import useCurrency from "@/utils/hooks/useCurrency";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { Add } from "@mui/icons-material";
import { isNaN, uniqueId } from "lodash";
import Image from "next/image";
import { useEffect, useMemo, useState } from "react";
import { Controller, useFieldArray, useFormContext, useWatch } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import BulkVariantEdit, { TFormData } from "../BulkVariantEdit/BulkVariantEdit";
import VariantsTable from "../DefineVariantsTable";
import AddVariantModal from "../DefineVariantsTable/AddVariantModal";
import { productAuthenticity, productConditions } from "../ProductForm/utils";
import { IOptionVatiantsFromProps } from "./types";
import { calculateSellingPriceFormula, makeOptionsBasedOnVariants, updateVariantsBasedOnOptions } from "./utils";

const OptionVariantsFrom = (props: IOptionVatiantsFromProps) => {
  const { t } = useTranslation();
  const { showModal, hideModal } = useModal();

  const { canAddOrEditOptions, isEdit } = props;

  const [currency] = useCurrency();
  const { symbol } = currency ?? { symbol: "" };

  const {
    control,
    setValue,
    clearErrors,
    formState: { errors }
  } = useFormContext<TProductForm>();
  const {
    fields: variants,
    replace,
    update
  } = useFieldArray({
    control,
    name: "variants",
    keyName: "key"
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const initialOptions = useMemo(() => makeOptionsBasedOnVariants(variants), [JSON.stringify(variants)]);
  const [options, setOptions] = useState(initialOptions);

  const calculateSellingPrice = ({ retailPrice, commission }: { retailPrice?: number; commission?: number }) => {
    return calculateSellingPriceFormula({ commission, retailPrice });
  };

  useEffect(() => {
    const newVariants = updateVariantsBasedOnOptions(options, variants);

    // we need `key` for .map key index. this os necessary when deleting a options
    // to make sure table renders correctly
    replace(newVariants?.map(v => ({ ...v, key: uniqueId() }) as any));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(options)]);

  const handleOpenVariantModal = (editIndex?: number) => {
    const isEdit = editIndex !== undefined;

    showModal({
      modalProps: { showCloseIcon: false },
      width: 420,
      body: (
        <AddVariantModal
          value={isEdit ? options?.[editIndex] : { name: "", values: [] }}
          onSubmit={value => {
            if (isEdit) {
              setOptions(prev => prev?.map((item, index) => (index !== editIndex ? item : value)));
            } else {
              setOptions(prev => [...(prev || []), value]);
            }
            hideModal();
          }}
          onCancel={hideModal}
          isEdit={isEdit}
        />
      )
    });
  };

  const handleBuklUpdate = (values: TFormData) => {
    const localData = [...(variants || [])];

    replace(localData?.map(item => ({ ...item, ...omitEmptyValues(values), key: uniqueId() })));
  };

  const handleOpenBulkEditModal = (editIndex?: number) => {
    const isEdit = editIndex !== undefined;

    showModal({
      modalProps: { showCloseIcon: false },
      width: 578,
      body: <BulkVariantEdit onSubmit={handleBuklUpdate} />
    });
  };

  return (
    <div className="flex gap-4 flex-col">
      <div className="flex items-center justify-between">
        <div className="text-v2-content-primary text-[13px] font-medium">{t("product.variants")}</div>

        {canAddOrEditOptions && options?.length < 3 && (
          <div
            className="flex items-center gap-1 text-xs font-semibold text-v2-content-on-action-2 cursor-pointer"
            onClick={() => handleOpenVariantModal()}
          >
            <Add className="size-[18px]" />
            {t("product.addVariant")}
          </div>
        )}
      </div>

      {options.map((option, index) => (
        <div key={option?.name} className="flex flex-col gap-2">
          <VariantsTable
            value={option}
            onClickEdit={() => handleOpenVariantModal(index)}
            onRemove={() => {
              setOptions(prev => prev.filter((_, i) => i !== index));
            }}
          />
        </div>
      ))}

      {!variants?.length && (
        <div className="py-9 rounded-md bg-v2-surface-secondary flex items-center justify-center flex-col gap-3">
          <Image src="/images/svgs/empty-folder-1.svg" alt="" width={60} height={60} />

          <div className="flex flex-col gap-1 items-center">
            <div className="text-v2-content-primary text-[13px] font-medium">{t("product.noVariantsPlaceholder")}</div>
            <div className="text-v2-content-primary text-[13px] font-medium text-center">
              {t("product.noVariantsPlaceholderDesc")}
            </div>
          </div>
        </div>
      )}
      {!!variants?.length && (
        <div className="flex flex-col gap-2">
          <div className="text-v2-content-primary text-[13px] font-medium">{t("product.productVariants")}</div>

          <div className="bg-v2-surface-primary rounded-lg border-2 border-v2-surface-thertiary p-4 flex flex-col gap-4">
            <div className="flex justify-between gap-1 items-start">
              <div className="flex-1 flex flex-col gap-1">
                <div className="text-sm font-medium text-v2-content-primary">
                  {t("product.variantsBulkOperation.title")}
                </div>
                <div className="max-w-lg font-medium text-[13px] text-v2-content-tertiary">
                  {t("product.variantsBulkOperation.desc")}
                </div>
              </div>
              <div>
                <CustomButton color="primary" className="!px-4 !py-2" onClick={() => handleOpenBulkEditModal()}>
                  {t("product.variantsBulkOperation.add")}
                </CustomButton>
              </div>
            </div>
            <div className="p-2 bg-v2-surface-info rounded-md flex items-center gap-1.5 text-[13px] font-medium">
              <Icon icon="solar:info-circle-outline" className="size-5" /> {t("product.variantsBulkOperation.hint")}
            </div>
          </div>

          <div className="divide-y-2 divide-v2-surface-thertiary rounded-md border-2 border-v2-surface-thertiary">
            {variants?.map((variant, index) => {
              return (
                <VariantWatcher index={index} key={(variant as any)?.key || variant?.id}>
                  {variantWatch => (
                    <Collapse2
                      key={(variant as any)?.key || variant?.id}
                      startAdornment={
                        <div className="flex flex-col gap-1">
                          <div className="text-[15px] font-medium text-v2-content-primary capitalize">
                            {variant?.options
                              ? Object.entries(variant.options)
                                  .map(item => `${item?.[0]}: ${item?.[1]}`)
                                  .join(" | ")
                              : "-"}
                          </div>
                          <div className="text-v2-content-tertiary text-xs font-normal hidden md:flex gap-6">
                            <div>
                              <span>{t("inventory")}: </span>
                              {variant.inventory ? toCommas(variant.inventory) : "-"}
                            </div>
                            <div>
                              <span>{t("product.listingPrice")}: </span>
                              {variant.retail_price ? toCommas(variant.retail_price) : "-"} {symbol}
                            </div>
                          </div>
                        </div>
                      }
                      endAdornment={
                        <Controller
                          key={index}
                          name={`variants.${index}.is_active`}
                          control={control}
                          render={({ field: { value, onChange } }) => (
                            <CustomSwitch
                              checked={value}
                              label={t("supplier.profile.active")}
                              labelClassName="ml-0 block"
                              onChange={e => {
                                onChange(e.target.checked);
                              }}
                            />
                          )}
                        />
                        // <CustomSwitch
                        //   name={`variants.${index}.status`}
                        //   label={t("supplier.profile.active")}
                        //   labelClassName="ml-0 hidden md:block"
                        //   checked={watch(`variants.${index}.status`) === VARIANT_STATUS_ACTIVE}
                        //   onChange={e => {
                        //     const checked = e.target.checked;
                        //     const newStatus = checked ? VARIANT_STATUS_ACTIVE : VARIANT_STATUS_INACTIVE;

                        //     console.log("index", index, "checked", checked, "newStatus", newStatus);

                        //     setValue(`variants.${index}.status`, newStatus, { shouldValidate: true, shouldDirty: true });
                        //   }}
                        // />
                      }
                    >
                      <div className="flex flex-col gap-4 mt-1">
                        <p className="text-v2-content-tertiary text-body4-medium pt-6 border-t border-t-v2-border-secondary">
                          {t("product.productConditionsTitle")}
                        </p>
                        <div className="flex 2xs:flex-nowrap flex-wrap items-center xmd:gap-4 gap-2 ">
                          {" "}
                          {productConditions?.map(item => (
                            <div
                              className={twMerge(
                                "border border-solid border-spacing-[2px] flex justify-between gap-2 h-full rounded-lg xmd:px-4 px-2 xmd:py-3 py-2 cursor-pointer w-full",
                                item?.id === variantWatch?.condition ? "border-purple-500" : "border-gray-50"
                              )}
                              key={item?.id}
                              onClick={() => {
                                setValue(`variants.${index}.condition`, item?.id);
                                clearErrors(`variants.${index}.condition`);
                              }}
                            >
                              <span className="text-body4-medium text-gray-999 whitespace-nowrap">{item?.title}</span>
                              {item?.id === variantWatch?.condition ? (
                                <Image src="/images/svgs/user-checked.svg" alt="realUser" width={24} height={24} />
                              ) : (
                                <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={24} height={24} />
                              )}
                            </div>
                          ))}
                        </div>
                        {errors?.variants?.[index]?.condition?.message && (
                          <InputHelper error>{errors?.variants[index]?.condition?.message}</InputHelper>
                        )}

                        <span className="text-v2-content-tertiary text-body4-medium">
                          {t("product.productAuthenticityTitle")}
                        </span>
                        <div className="flex 2xs:flex-nowrap flex-wrap items-center xmd:gap-4 gap-2 ">
                          {" "}
                          {productAuthenticity?.map(item => (
                            <div
                              className={twMerge(
                                "border border-solid border-spacing-[2px] flex justify-between gap-2 h-full rounded-lg xmd:px-4 px-2 xmd:py-3 py-2 cursor-pointer w-full",
                                item?.id === variantWatch?.authenticity ? "border-purple-500" : "border-gray-50"
                              )}
                              key={item?.id}
                              onClick={() => {
                                setValue(`variants.${index}.authenticity`, item?.id);
                                clearErrors(`variants.${index}.authenticity`);
                              }}
                            >
                              <span className="text-body4-medium text-gray-999 whitespace-nowrap">{item?.title}</span>
                              {item?.id === variantWatch?.authenticity ? (
                                <Image src="/images/svgs/user-checked.svg" alt="realUser" width={24} height={24} />
                              ) : (
                                <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={24} height={24} />
                              )}
                            </div>
                          ))}
                        </div>
                        {errors?.variants?.[index]?.authenticity?.message && (
                          <InputHelper error>{errors?.variants[index]?.authenticity?.message}</InputHelper>
                        )}

                        <div className="flex items-center justify-between py-6 border-y border-y-v2-border-secondary my-5">
                          <div className="flex flex-col">
                            <span className="text-v2-content-primary text-body3-medium !font-semibold">
                              {t("product.preOrderTitle")}
                            </span>
                            <span className="text-v2-content-tertiary text-caption-medium">
                              {t("product.preOrderSubTitle")}
                            </span>
                          </div>

                          <div className="flex items-center gap-2 ">
                            <span className="text-v2-content-tertiary text-caption-medium">
                              {t("supplier.profile.active")}
                            </span>
                            <Controller
                              name={`variants.${index}.backorder`}
                              control={control}
                              render={({ field }) => (
                                <CustomSwitch
                                  checked={field.value}
                                  onChange={(e, checked) => field.onChange(checked)}
                                  labelClassName="!ml-0 !mr-0"
                                  textClassName="hidden"
                                  size="small"
                                />
                              )}
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
                          <div>
                            <Controller
                              name={`variants.${index}.inventory`}
                              control={control}
                              render={({ field, fieldState: { error } }) => (
                                <NumberInput
                                  {...field}
                                  onChange={e => {
                                    if (e?.target?.value === undefined || isNaN(e?.target?.value)) return;

                                    const value = Number(e?.target?.value);
                                    field?.onChange(value);
                                    // Update selling price after inventory change
                                    // setTimeout(updateSellingPrice, 0);
                                  }}
                                  value={field?.value}
                                  autoComplete="off"
                                  label={t("product.inventory")}
                                  placeholder={t("product.inventory")}
                                  error={Boolean(error?.message)}
                                  helperText={error?.message || ""}
                                  endAdornment={
                                    <div className="text-v2-content-tertiary text-body4-medium pr-0.5">
                                      {t("count")}
                                    </div>
                                  }
                                />
                              )}
                            />
                          </div>

                          <div>
                            <Controller
                              name={`variants.${index}.retail_price`}
                              control={control}
                              render={({ field, fieldState: { error } }) => (
                                <PriceInput
                                  {...field}
                                  id={field?.name}
                                  autoComplete="off"
                                  value={field?.value}
                                  label={t("productForm.retailPrice")}
                                  placeholder={t("productForm.retailPricePlaceholder")}
                                  error={Boolean(error?.message)}
                                  helperText={error?.message || ""}
                                  onChange={e => {
                                    field.onChange(e);
                                    // Update selling price after retail price change
                                    // setTimeout(updateSellingPrice, 0);
                                  }}
                                />
                              )}
                            />

                            <div className="text-xs leading-4 text-v2-content-tertiary mt-1">
                              {t("productForm.retailPriceTooltipDesc")}
                            </div>
                          </div>

                          <div>
                            <Controller
                              name={`variants.${index}.commission`}
                              control={control}
                              render={({ field, fieldState: { error } }) => (
                                <PriceInput
                                  {...field}
                                  id={field?.name}
                                  value={field?.value}
                                  autoComplete="off"
                                  label={t("productForm.salesCommissionPercentage")}
                                  placeholder={t("productForm.commisionPlaceholder")}
                                  error={Boolean(error?.message)}
                                  helperText={error?.message || ""}
                                  endAdornment={
                                    <div className="text-v2-content-tertiary text-body4-medium pr-0.5">
                                      {t("percent")}
                                    </div>
                                  }
                                  onChange={e => {
                                    field.onChange(e);
                                    // Update selling price after commission change
                                    // setTimeout(updateSellingPrice, 0);
                                  }}
                                />
                              )}
                            />

                            <div className="text-xs leading-4 text-v2-content-tertiary mt-1">
                              {t("product.commissionHint")}
                            </div>
                          </div>

                          <div>
                            <PriceInput
                              autoComplete="off"
                              readOnly
                              requiredStar={false}
                              inputParentClassName="bg-v2-surface-secondary border-v2-surface-secondary"
                              value={calculateSellingPrice({
                                commission: variantWatch?.commission,
                                retailPrice: variantWatch?.retail_price
                              })}
                              label={t("productForm.yourSellingPrice")}
                              placeholder="-"
                            />
                          </div>

                          <AdvancedPrice index={index} map={variantWatch?.map} sku={variantWatch?.sku} />
                          {/* <div className="flex items-center gap-2 mt-3 mb-2 ">
                            <CustomSwitch
                              checked={pricingProChecked || !!variantWatch?.map || !!variantWatch?.sku}
                              onChange={(e, checked) => {
                                setPricingProChecked(checked);
                                if (!checked) {
                                  setValue("map", undefined);
                                  setValue("sku", undefined);
                                }
                              }}
                              labelClassName="!ml-0 !mr-0"
                              textClassName="hidden"
                            />
                            <span className="text-body4-medium">{t("productForm.pricingProStatus")}</span>
                          </div>
                          <div />

                          {(pricingProChecked || !!variantWatch?.map || !!variantWatch?.sku) && (
                            <>
                              <div>
                                <Controller
                                  name={`variants.${index}.map`}
                                  control={control}
                                  render={({ field, fieldState: { error } }) => (
                                    <PriceInput
                                      {...field}
                                      id={field?.name}
                                      autoComplete="off"
                                      label={t("productForm.minimumSalesCommissionPercentage")}
                                      placeholder={t("productForm.minCommisionPlaceholder")}
                                      error={Boolean(error?.message)}
                                      endAdornment={
                                        <div className="text-v2-content-tertiary text-body4-medium pr-0.5">
                                          {t("percent")}
                                        </div>
                                      }
                                      helperText={error?.message || ""}
                                    />
                                  )}
                                />

                                <div className="text-xs leading-4 text-v2-content-tertiary mt-1">
                                  {t("product.minComissionHint")}
                                </div>
                              </div>

                              <div>
                                <Controller
                                  name={`variants.${index}.sku`}
                                  control={control}
                                  render={({ field, fieldState: { error } }) => (
                                    <Input
                                      {...field}
                                      id={field?.name}
                                      optional
                                      autoComplete="off"
                                      label={t("productForm.skuLabel")}
                                      placeholder={t("productForm.skuPlaceholder")}
                                      requiredStar={false}
                                      required={false}
                                      error={Boolean(error?.message)}
                                      helperText={error?.message || ""}
                                    />
                                  )}
                                />
                                <div className="text-xs leading-4 text-v2-content-tertiary mt-1">
                                  {t("product.skuHint")}
                                </div>
                              </div>
                            </>
                          )} */}
                        </div>

                        {/* <Controller
                          name={`variants.${index}.is_active`}
                          control={control}
                          key={(variant as any)?.key || variant?.id}
                          render={({ field, fieldState: { error } }) => (
                            <CustomSwitch
                              name={field?.name}
                              label={t("supplier.profile.active")}
                              labelClassName="ml-0 md:hidden"
                              checked={field?.value}
                              onChange={(e, v) => field?.onChange(v)}
                            />
                          )}
                        /> */}
                      </div>
                    </Collapse2>
                  )}
                </VariantWatcher>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default OptionVariantsFrom;

type VariantWatcherProps = {
  index: number;
  children: (variant: TProductForm["variants"][number]) => React.ReactNode;
};

const VariantWatcher = ({ index, children }: VariantWatcherProps) => {
  const { control } = useFormContext<TProductForm>();

  const variant = useWatch({
    control,
    name: `variants.${index}`
  });

  return <>{children(variant)}</>;
};

const AdvancedPrice = ({ index, map, sku }: { index: number; map?: number; sku?: string }) => {
  const { t } = useTranslation();
  const { control, setValue } = useFormContext<TProductForm>();

  const [pricingProChecked, setPricingProChecked] = useState(() => !!map || !!sku || false);

  return (
    <>
      <div className="flex items-center gap-2 mt-3 mb-2 ">
        <CustomSwitch
          checked={pricingProChecked}
          onChange={(e, checked) => {
            setPricingProChecked(checked);
            if (!checked) {
              setValue(`variants.${index}.map`, undefined);
              setValue(`variants.${index}.sku`, undefined);
            }
          }}
          labelClassName="!ml-0 !mr-0"
          textClassName="hidden"
        />
        <span className="text-body4-medium">{t("productForm.pricingProStatus")}</span>
      </div>
      <div />

      {pricingProChecked && (
        <>
          <div>
            <Controller
              name={`variants.${index}.map`}
              control={control}
              render={({ field, fieldState: { error } }) => (
                <PriceInput
                  {...field}
                  id={field?.name}
                  autoComplete="off"
                  requiredStar={false}
                  label={t("productForm.minimumSalesCommissionPercentage")}
                  placeholder={t("productForm.minCommisionPlaceholder")}
                  error={Boolean(error?.message)}
                  endAdornment={<div className="text-v2-content-tertiary text-body4-medium pr-0.5">{t("percent")}</div>}
                  helperText={error?.message || ""}
                />
              )}
            />

            <div className="text-xs leading-4 text-v2-content-tertiary mt-1">{t("product.minComissionHint")}</div>
          </div>

          <div>
            <Controller
              name={`variants.${index}.sku`}
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  id={field?.name}
                  optional
                  autoComplete="off"
                  label={t("productForm.skuLabel")}
                  placeholder={t("productForm.skuPlaceholder")}
                  requiredStar={false}
                  required={false}
                  className="!text-right"
                  dir="ltr"
                  error={Boolean(error?.message)}
                  helperText={error?.message || ""}
                />
              )}
            />
            <div className="text-xs leading-4 text-v2-content-tertiary mt-1">{t("product.skuHint")}</div>
          </div>
        </>
      )}
    </>
  );
};
