// middleware.ts
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getValidSubdomain } from "@/utils/subdomain";
import { pathToRegexp } from "path-to-regexp";
import { SESSION_DATA } from "@/constants/cookies";
import { routes } from "./constants/routes";
import { SessionData } from "./store/zustand/sessionStore";

export const getEnvPath = (devPath: string, prodPath: string) => {
  return process.env.NODE_ENV === "development" ? devPath : prodPath;
};
const authRequiredPaths = ["/((?!api|_next/static|_next/image|favicon.ico|manifest.json|assets|images).*)"];

// export default withAuth(
export async function middleware(req: NextRequest) {
  // Paths for supplier based on environment
  const loginPath = getEnvPath(routes.supplierLogin, routes.login);
  const registerPath = getEnvPath(routes.supplierRegister, routes.register);
  const changePasswordPath = getEnvPath(routes.supplierChangePassword, routes.changePassword);

  // Paths for retailer based on environment
  const retailerLoginPath = getEnvPath(routes.retailerLogin, routes.login);
  const retailerRegisterPath = getEnvPath(routes.retailerRegister, routes.register);
  const retailerChangePasswordPath = getEnvPath(routes.retailerChangePassword, routes.changePassword);

  const authRequiredPathsWhiteList = [
    loginPath,
    registerPath,
    changePasswordPath,
    retailerLoginPath,
    retailerRegisterPath,
    retailerChangePasswordPath
  ];

  const url = req.nextUrl.clone();
  const host = req.headers.get("host");

  const hasTokenParams = !!url.searchParams.has("token");

  const sessionCookie = req.cookies.get(SESSION_DATA)?.value;
  const sessionData: SessionData | null = !!sessionCookie ? JSON.parse(sessionCookie) : null;
  const token = sessionData?.access_token;

  // Extract pathname using req.nextUrl.pathname to ensure you are processing the correct route.
  const pathname = req.nextUrl.pathname;

  const isAuthRequiredPath =
    pathToRegexp(authRequiredPaths).test(pathname || "") &&
    !pathToRegexp(authRequiredPathsWhiteList).test(pathname || "");

  const isLoggedIn = !!token;

  // if user is logged in and tries to access login page, redirect to home page
  if (isLoggedIn && pathToRegexp(authRequiredPathsWhiteList).test(pathname || "") && !hasTokenParams) {
    return NextResponse.redirect(new URL(routes.home, req.url));
  }

  // if user is not logged in and tries to access restricted area, redirect to login page
  if (isAuthRequiredPath && !isLoggedIn && !hasTokenParams) {
    return NextResponse.redirect(new URL(loginPath, req.url));
  }

  /* --------------- detect and redirect to specific page folder -------------- */
  if (process.env.NODE_ENV === "development" || host === "retentify.ai") return;

  const subdomain = getValidSubdomain(host);

  if (subdomain) {
    if (!token || subdomain === sessionData?.user_type?.toLocaleLowerCase()) {
      url.pathname = `/${subdomain}${url.pathname}`;
    } else {
      url.pathname = "/401";
    }
  }

  return NextResponse.rewrite(url);
}
export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|manifest.json|assets|images).*)"]
};
